# ERP模块500错误快速修复指南

## 🚨 问题现状
用户反馈：点击ERP模块字段时出现"服务器错误请联系管理员"（HTTP 500错误）

## ✅ 已完成的修复工作

### 1. 后端修复（已完成）
- ✅ 更新了数据库表结构（用户已导入新数据库）
- ✅ 修改了后端实体类：ErpCustomerDO, ErpSaleOrderDO, ErpSaleOrderItemDO, ErpProductDO
- ✅ 更新了VO类：ErpSaleOrderSaveReqVO, ErpSaleOrderRespVO, ErpCustomerSaveReqVO, ProductSaveReqVO
- ✅ 更新了前端API接口定义

### 2. 前端修复（已完成）
- ✅ 客户管理页面：新增ERP字段（客户编号、客户名称、发货信息等）
- ✅ 销售订单页面：新增ERP字段（ERP订单号、生产计划号、交货日期等）
- ✅ 表单字段优化：ERP订单号为用户输入，订单单号为系统生成
- ✅ 列表显示：优先显示ERP相关字段

## 🔧 下一步测试步骤

### 步骤1：启动后端服务
```bash
# 方法1：使用Maven启动
cd e:\work\yudao\ruoyi-vue-pro
mvn spring-boot:run -pl yudao-server -Dspring-boot.run.profiles=local

# 方法2：如果有编译好的jar文件
java -jar yudao-server/target/yudao-server.jar --spring.profiles.active=local
```

### 步骤2：启动前端服务
```bash
cd e:\work\yudao\ruoyi-vue-pro\yudao-ui\yudao-ui-admin-vue3
npm run dev
```

### 步骤3：测试功能
1. **登录系统**：访问前端地址（通常是 http://localhost:3000）
2. **测试客户管理**：
   - 进入 ERP -> 客户管理
   - 点击"新增"按钮
   - 填写ERP字段：客户编号、客户名称、发货地址等
   - 保存并检查是否成功
3. **测试销售订单**：
   - 进入 ERP -> 销售订单
   - 点击"新增"按钮
   - 填写ERP字段：ERP订单号、生产计划号、交货日期等
   - 保存并检查是否成功

## 🐛 如果仍有500错误

### 检查后端日志
查看控制台输出的错误信息，重点关注：
- 数据库连接错误
- SQL执行错误（字段不存在）
- 实体类映射错误

### 常见问题排查
1. **数据库连接**：确认MySQL服务运行，数据库`ruoyi-vue-pro`存在
2. **表结构**：确认表中包含新的ERP字段
3. **字段映射**：确认实体类字段名与数据库列名匹配

### 验证数据库表结构
连接MySQL执行：
```sql
-- 检查客户表结构
DESCRIBE erp_customer;
-- 应该包含：customer_id, customer_name, delivery_address, delivery_country, delivery_tel, delivery_way

-- 检查销售订单表结构
DESCRIBE erp_sale_order;
-- 应该包含：order_id, due_date, order_pno, mrchds, wkspcd, season, style_code等
```

## 📋 成功标志
修复成功后应该看到：
- ✅ 不再出现"服务器错误请联系管理员"
- ✅ 客户新增功能正常，可以输入ERP字段
- ✅ 销售订单新增功能正常，可以输入ERP字段
- ✅ 列表正确显示ERP相关字段
- ✅ 搜索功能正常工作

## 🎯 关键修复点总结
1. **数据库表结构**：已包含所有新的ERP字段
2. **后端实体类**：已同步更新所有字段
3. **前端页面**：已更新表单和列表显示
4. **字段重复问题**：已解决，ERP订单号和系统订单号功能明确区分
