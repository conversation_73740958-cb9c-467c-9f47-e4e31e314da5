# ERP字段映射表

## 映射策略
根据业务逻辑将6个字段文档中的所有字段映射到现有ERP表中：

### 1. erp_sale_order (销售订单主表)
**订单主要信息字段**
- orderId (string) - 订单号
- createdAt (string) - 排料完成时间  
- dueDate (string) - 交货日期
- orderPno (string) - 生产计划号
- ppFrom (string) - 订单来源系统
- thirdUid (string) - 订单唯一主键(来源系统)
- orderNo (string) - 销售订单号
- orderDate (string) - 下单日期
- orderTotal (integer) - 订单数量
- remark (string) - 订单备注
- mrchds (string) - 业务员
- wkspcd (string) - 指定车间
- status (string) - 订单状态
- season (string) - 款式季节

**排料结果相关字段(JSON格式存储)**
- patternFileUrl (string) - 纸样文件URL
- styleCode (string) - 款式编码
- extra (string) - 附加内容
- stylePatterns (JSON) - 版型数组
- sizes (JSON) - 尺码表
- dosages (JSON) - 物料用量表
- materialMarkers (JSON) - 排料结果
- mcSizes (JSON) - 毛裁尺寸表
- fullSizeTechs (JSON) - 齐码工艺尺寸表
- technologies (JSON) - 生产工艺表

**撤销相关字段**
- modifyBy (string) - 撤销操作人
- modifyReason (string) - 撤销原因

### 2. erp_sale_order_items (销售订单明细表)
**订单明细字段**
- itemQty (integer) - 数量
- sizeNo (string) - 尺码
- orderDno (string) - PO单号
- prodId (string) - 品类编号
- stylId (string) - 款式编号
- mainMid (string) - 主料(面料)编号
- mainMcolor (string) - 主料(面料)颜色
- mainMname (string) - 主料(面料)名称
- mainSpec (string) - 主料(面料)规格
- prodName (string) - 品类名称
- stylName (string) - 款式名称
- progtype (string) - 产品类型
- skuName (string) - 产品名称
- skuSpec (string) - 产品规格
- finishedSize (string) - 成品尺寸
- unitnm (string) - 单位

**消费者信息字段(JSON格式存储)**
- cusadr (string) - 消费者地址
- cushgt (string) - 消费者身高
- cusicd (string) - 消费者加款唯一ID
- cusnam (string) - 消费者名称
- cusncd (string) - 消费者编号
- cussex (string) - 消费者性别
- custel (string) - 消费者电话
- cuswgt (string) - 消费者体重
- deptnm (string) - 消费者部门
- msutnm (string) - 量体师名称
- surList (JSON) - 量体明细

**物料清单字段(JSON格式存储)**
- bomList (JSON) - 物料清单
- tecList (JSON) - 工艺清单
- docList (JSON) - 款式文件

### 3. erp_customer (客户表)
**客户相关字段**
- customerId (string) - 客户编号
- customerName (string) - 客户名称
- deliveryAddress (string) - 发货地址
- deliveryCountry (string) - 发货国家
- deliveryTel (string) - 发货联系
- devliveryWay (string) - 发货方式

### 4. erp_product (产品表)
**产品/物料相关字段**
- materialId (string) - 物料编号
- materialName (string) - 物料名称
- materialTypeId (string) - 物料分类编号
- materialTypeName (string) - 物料分类名称
- materialColor (string) - 物料颜色编号
- materialClrnm (string) - 物料颜色名称
- materialCom (string) - 物料成份
- materialUsage (string) - 物料用途
- materialBatchno (string) - 物料批次
- cut_width (string) - 裁剪宽度
- cut_wstrat (string) - 裁剪用量
- tolConsu (number) - 总用量
- uniConsu (number) - 物料单耗
- unitcd (string) - 单位编号
- width (number) - 物料宽度
- mainMterial (boolean) - 主料(非常备)/辅料(常备)

### 5. 系统字段保留
所有表保留以下系统字段：
- id (bigint) - 主键ID
- creator (string) - 创建者
- create_time (datetime) - 创建时间
- updater (string) - 更新者
- update_time (datetime) - 更新时间
- deleted (bit) - 是否删除
- tenant_id (bigint) - 租户编号

## 复杂结构处理
对于多层级的复杂字段结构，使用JSON类型字段存储，保持原有的层级关系。
