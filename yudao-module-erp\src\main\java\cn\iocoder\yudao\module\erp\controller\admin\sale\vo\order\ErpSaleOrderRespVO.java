package cn.iocoder.yudao.module.erp.controller.admin.sale.vo.order;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "管理后台 - ERP 销售订单 Response VO")
@Data
@ExcelIgnoreUnannotated
public class ErpSaleOrderRespVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "17386")
    @ExcelProperty("编号")
    private Long id;

    @Schema(description = "销售单编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "XS001")
    @ExcelProperty("销售单编号")
    private String no;

    @Schema(description = "销售状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("销售状态")
    private Integer status;

    @Schema(description = "客户编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1724")
    private Long customerId;
    @Schema(description = "客户名称", example = "芋道")
    @ExcelProperty("客户名称")
    private String customerName;

    @Schema(description = "结算账户编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "311.89")
    @ExcelProperty("结算账户编号")
    private Long accountId;

    @Schema(description = "销售员编号", example = "1888")
    private Long saleUserId;

    @Schema(description = "下单时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("下单时间")
    private LocalDateTime orderTime;

    @Schema(description = "合计数量", requiredMode = Schema.RequiredMode.REQUIRED, example = "15663")
    @ExcelProperty("合计数量")
    private BigDecimal totalCount;
    @Schema(description = "最终合计价格", requiredMode = Schema.RequiredMode.REQUIRED, example = "24906")
    @ExcelProperty("最终合计价格")
    private BigDecimal totalPrice;

    @Schema(description = "合计产品价格，单位：元", requiredMode = Schema.RequiredMode.REQUIRED, example = "7127")
    private BigDecimal totalProductPrice;

    @Schema(description = "合计税额，单位：元", requiredMode = Schema.RequiredMode.REQUIRED, example = "7127")
    private BigDecimal totalTaxPrice;

    @Schema(description = "优惠率，百分比", requiredMode = Schema.RequiredMode.REQUIRED, example = "99.88")
    private BigDecimal discountPercent;

    @Schema(description = "优惠金额，单位：元", requiredMode = Schema.RequiredMode.REQUIRED, example = "7127")
    private BigDecimal discountPrice;

    @Schema(description = "定金金额，单位：元", requiredMode = Schema.RequiredMode.REQUIRED, example = "7127")
    private BigDecimal depositPrice;

    @Schema(description = "附件地址", example = "https://www.iocoder.cn")
    @ExcelProperty("附件地址")
    private String fileUrl;

    @Schema(description = "备注", example = "你猜")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "创建人", example = "芋道")
    private String creator;
    @Schema(description = "创建人名称", example = "芋道")
    private String creatorName;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "订单项列表", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<Item> items;

    @Schema(description = "产品信息", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("产品信息")
    private String productNames;

    // ========== 销售出库 ==========

    @Schema(description = "销售出库数量", requiredMode = Schema.RequiredMode.REQUIRED, example = "100.00")
    private BigDecimal outCount;

    // ========== 销售退货（出库）） ==========

    @Schema(description = "销售退货数量", requiredMode = Schema.RequiredMode.REQUIRED, example = "100.00")
    private BigDecimal returnCount;

    // ========== 新增ERP扩展字段 ==========
    @Schema(description = "ERP订单号", example = "ORD001")
    private String orderId;

    @Schema(description = "排料完成时间", example = "2024-01-01")
    private String createdAt;

    @Schema(description = "交货日期", example = "2024-01-01")
    private String dueDate;

    @Schema(description = "生产计划号", example = "PNO001")
    private String orderPno;

    @Schema(description = "订单来源系统", example = "ERP")
    private String ppFrom;

    @Schema(description = "订单唯一主键(来源系统)", example = "UID001")
    private String thirdUid;

    @Schema(description = "ERP销售订单号", example = "SO001")
    private String orderNo;

    @Schema(description = "下单日期", example = "2024-01-01")
    private String orderDate;

    @Schema(description = "订单数量", example = "100")
    private Integer orderTotal;

    @Schema(description = "业务员", example = "张三")
    private String mrchds;

    @Schema(description = "指定车间", example = "车间A")
    private String wkspcd;

    @Schema(description = "ERP订单状态", example = "待生产")
    private String erpStatus;

    @Schema(description = "款式季节", example = "春季")
    private String season;

    @Schema(description = "纸样文件URL", example = "http://example.com/pattern.pdf")
    private String patternFileUrl;

    @Schema(description = "款式编码", example = "STYLE001")
    private String styleCode;

    @Schema(description = "附加内容", example = "特殊要求")
    private String extra;

    @Schema(description = "版型数组", example = "{}")
    private String stylePatterns;

    @Schema(description = "尺码表", example = "{}")
    private String sizes;

    @Schema(description = "物料用量表", example = "{}")
    private String dosages;

    @Schema(description = "排料结果", example = "{}")
    private String materialMarkers;

    @Schema(description = "毛裁尺寸表", example = "{}")
    private String mcSizes;

    @Schema(description = "齐码工艺尺寸表", example = "{}")
    private String fullSizeTechs;

    @Schema(description = "生产工艺表", example = "{}")
    private String technologies;

    @Schema(description = "撤销操作人", example = "李四")
    private String modifyBy;

    @Schema(description = "撤销原因", example = "客户要求")
    private String modifyReason;

    @Data
    public static class Item {

        @Schema(description = "订单项编号", example = "11756")
        private Long id;

        @Schema(description = "产品编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "3113")
        private Long productId;

        @Schema(description = "产品单位单位", requiredMode = Schema.RequiredMode.REQUIRED, example = "3113")
        private Long productUnitId;

        @Schema(description = "产品单价", example = "100.00")
        private BigDecimal productPrice;

        @Schema(description = "产品数量", requiredMode = Schema.RequiredMode.REQUIRED, example = "100.00")
        @NotNull(message = "产品数量不能为空")
        private BigDecimal count;

        @Schema(description = "税率，百分比", example = "99.88")
        private BigDecimal taxPercent;

        @Schema(description = "税额，单位：元", example = "100.00")
        private BigDecimal taxPrice;

        @Schema(description = "备注", example = "随便")
        private String remark;

        // ========== 销售出库 ==========

        @Schema(description = "销售出库数量", requiredMode = Schema.RequiredMode.REQUIRED, example = "100.00")
        private BigDecimal outCount;

        // ========== 销售退货（入库）） ==========

        @Schema(description = "销售退货数量", requiredMode = Schema.RequiredMode.REQUIRED, example = "100.00")
        private BigDecimal returnCount;

        // ========== 关联字段 ==========

        @Schema(description = "产品名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "巧克力")
        private String productName;
        @Schema(description = "产品条码", requiredMode = Schema.RequiredMode.REQUIRED, example = "A9985")
        private String productBarCode;
        @Schema(description = "产品单位名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "盒")
        private String productUnitName;

        @Schema(description = "库存数量", requiredMode = Schema.RequiredMode.REQUIRED, example = "100.00")
        private BigDecimal stockCount; // 该字段仅仅在“详情”和“编辑”时使用

    }

}