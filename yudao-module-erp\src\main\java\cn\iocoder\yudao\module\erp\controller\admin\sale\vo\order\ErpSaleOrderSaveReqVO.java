package cn.iocoder.yudao.module.erp.controller.admin.sale.vo.order;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "管理后台 - ERP 销售订单新增/修改 Request VO")
@Data
public class ErpSaleOrderSaveReqVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "17386")
    private Long id;

    @Schema(description = "客户编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1724")
    @NotNull(message = "客户编号不能为空")
    private Long customerId;

    @Schema(description = "下单时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "下单时间不能为空")
    private LocalDateTime orderTime;

    @Schema(description = "销售员编号", example = "1888")
    private Long saleUserId;

    @Schema(description = "结算账户编号", example = "31189")
    private Long accountId;

    @Schema(description = "优惠率，百分比", requiredMode = Schema.RequiredMode.REQUIRED, example = "99.88")
    private BigDecimal discountPercent;

    @Schema(description = "定金金额，单位：元", example = "7127")
    private BigDecimal depositPrice;

    @Schema(description = "附件地址", example = "https://www.iocoder.cn")
    private String fileUrl;

    @Schema(description = "备注", example = "你猜")
    private String remark;

    // ========== 新增ERP扩展字段 ==========
    @Schema(description = "ERP订单号", example = "ORD001")
    private String orderId;

    @Schema(description = "排料完成时间", example = "2024-01-01")
    private String createdAt;

    @Schema(description = "交货日期", example = "2024-01-01")
    private String dueDate;

    @Schema(description = "生产计划号", example = "PNO001")
    private String orderPno;

    @Schema(description = "订单来源系统", example = "ERP")
    private String ppFrom;

    @Schema(description = "订单唯一主键(来源系统)", example = "UID001")
    private String thirdUid;

    @Schema(description = "ERP销售订单号", example = "SO001")
    private String orderNo;

    @Schema(description = "下单日期", example = "2024-01-01")
    private String orderDate;

    @Schema(description = "订单数量", example = "100")
    private Integer orderTotal;

    @Schema(description = "业务员", example = "张三")
    private String mrchds;

    @Schema(description = "指定车间", example = "车间A")
    private String wkspcd;

    @Schema(description = "ERP订单状态", example = "待生产")
    private String erpStatus;

    @Schema(description = "款式季节", example = "春季")
    private String season;

    @Schema(description = "纸样文件URL", example = "http://example.com/pattern.pdf")
    private String patternFileUrl;

    @Schema(description = "款式编码", example = "STYLE001")
    private String styleCode;

    @Schema(description = "附加内容", example = "特殊要求")
    private String extra;

    @Schema(description = "版型数组", example = "{}")
    private String stylePatterns;

    @Schema(description = "尺码表", example = "{}")
    private String sizes;

    @Schema(description = "物料用量表", example = "{}")
    private String dosages;

    @Schema(description = "排料结果", example = "{}")
    private String materialMarkers;

    @Schema(description = "毛裁尺寸表", example = "{}")
    private String mcSizes;

    @Schema(description = "齐码工艺尺寸表", example = "{}")
    private String fullSizeTechs;

    @Schema(description = "生产工艺表", example = "{}")
    private String technologies;

    @Schema(description = "撤销操作人", example = "李四")
    private String modifyBy;

    @Schema(description = "撤销原因", example = "客户要求")
    private String modifyReason;

    @Schema(description = "订单清单列表")
    private List<Item> items;

    @Data
    public static class Item {

        @Schema(description = "订单项编号", example = "11756")
        private Long id;

        @Schema(description = "产品编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "3113")
        @NotNull(message = "产品编号不能为空")
        private Long productId;

        @Schema(description = "产品单位单位", requiredMode = Schema.RequiredMode.REQUIRED, example = "3113")
        @NotNull(message = "产品单位单位不能为空")
        private Long productUnitId;

        @Schema(description = "产品单价", example = "100.00")
        private BigDecimal productPrice;

        @Schema(description = "产品数量", requiredMode = Schema.RequiredMode.REQUIRED, example = "100.00")
        @NotNull(message = "产品数量不能为空")
        private BigDecimal count;

        @Schema(description = "税率，百分比", example = "99.88")
        private BigDecimal taxPercent;

        @Schema(description = "备注", example = "随便")
        private String remark;

        // ========== 新增ERP扩展字段 ==========
        @Schema(description = "ERP数量", example = "10")
        private Integer itemQty;

        @Schema(description = "尺码", example = "L")
        private String sizeNo;

        @Schema(description = "PO单号", example = "PO001")
        private String orderDno;

        @Schema(description = "品类编号", example = "PROD001")
        private String prodId;

        @Schema(description = "款式编号", example = "STYLE001")
        private String stylId;

        @Schema(description = "主料(面料)编号", example = "MAT001")
        private String mainMid;

        @Schema(description = "主料(面料)颜色", example = "红色")
        private String mainMcolor;

        @Schema(description = "主料(面料)名称", example = "棉布")
        private String mainMname;

        @Schema(description = "主料(面料)规格", example = "100%棉")
        private String mainSpec;

        @Schema(description = "品类名称", example = "上衣")
        private String prodName;

        @Schema(description = "款式名称", example = "T恤")
        private String stylName;

        @Schema(description = "产品类型", example = "成衣")
        private String progtype;

        @Schema(description = "产品名称", example = "男士T恤")
        private String skuName;

        @Schema(description = "产品规格", example = "L码")
        private String skuSpec;

        @Schema(description = "成品尺寸", example = "180*90")
        private String finishedSize;

        @Schema(description = "单位", example = "件")
        private String unitnm;

        @Schema(description = "消费者地址", example = "北京市")
        private String cusadr;

        @Schema(description = "消费者身高", example = "175")
        private String cushgt;

        @Schema(description = "消费者加款唯一ID", example = "CUS001")
        private String cusicd;

        @Schema(description = "消费者名称", example = "张三")
        private String cusnam;

        @Schema(description = "消费者编号", example = "C001")
        private String cusncd;

        @Schema(description = "消费者性别", example = "男")
        private String cussex;

        @Schema(description = "消费者电话", example = "13800138000")
        private String custel;

        @Schema(description = "消费者体重", example = "70")
        private String cuswgt;

        @Schema(description = "消费者部门", example = "技术部")
        private String deptnm;

        @Schema(description = "量体师名称", example = "李四")
        private String msutnm;

        @Schema(description = "量体明细", example = "{}")
        private String surList;

        @Schema(description = "物料清单", example = "{}")
        private String bomList;

        @Schema(description = "工艺清单", example = "{}")
        private String tecList;

        @Schema(description = "款式文件", example = "{}")
        private String docList;

    }

}