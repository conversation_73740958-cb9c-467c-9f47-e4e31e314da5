package cn.iocoder.yudao.module.erp.dal.dataobject.product;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.math.BigDecimal;

/**
 * ERP 产品 DO
 *
 * <AUTHOR>
 */
@TableName("erp_product")
@KeySequence("erp_product_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ErpProductDO extends BaseDO {

    /**
     * 产品编号
     */
    @TableId
    private Long id;
    /**
     * 产品名称
     */
    private String name;
    /**
     * 产品条码
     */
    private String barCode;
    /**
     * 产品分类编号
     *
     * 关联 {@link ErpProductCategoryDO#getId()}
     */
    private Long categoryId;
    /**
     * 单位编号
     *
     * 关联 {@link ErpProductUnitDO#getId()}
     */
    private Long unitId;
    /**
     * 产品状态
     *
     * 枚举 {@link cn.iocoder.yudao.framework.common.enums.CommonStatusEnum}
     */
    private Integer status;
    /**
     * 产品规格
     */
    private String standard;
    /**
     * 产品备注
     */
    private String remark;
    /**
     * 保质期天数
     */
    private Integer expiryDay;
    /**
     * 基础重量（kg）
     */
    private BigDecimal weight;
    /**
     * 采购价格，单位：元
     */
    private BigDecimal purchasePrice;
    /**
     * 销售价格，单位：元
     */
    private BigDecimal salePrice;
    /**
     * 最低价格，单位：元
     */
    private BigDecimal minPrice;

    // ========== 新增ERP扩展字段 ==========
    /**
     * 物料编号
     */
    private String materialId;
    /**
     * 物料名称
     */
    private String materialName;
    /**
     * 物料分类编号
     */
    private String materialTypeId;
    /**
     * 物料分类名称
     */
    private String materialTypeName;
    /**
     * 物料颜色编号
     */
    private String materialColor;
    /**
     * 物料颜色名称
     */
    private String materialClrnm;
    /**
     * 物料成份
     */
    private String materialCom;
    /**
     * 物料用途
     */
    private String materialUsage;
    /**
     * 物料批次
     */
    private String materialBatchno;
    /**
     * 裁剪宽度
     */
    private String cutWidth;
    /**
     * 裁剪用量
     */
    private String cutWstrat;
    /**
     * 总用量
     */
    private BigDecimal tolConsu;
    /**
     * 物料单耗
     */
    private BigDecimal uniConsu;
    /**
     * 单位编号
     */
    private String unitcd;
    /**
     * 物料宽度
     */
    private BigDecimal width;
    /**
     * 主料(非常备)/辅料(常备)
     */
    private Boolean mainMaterial;

}