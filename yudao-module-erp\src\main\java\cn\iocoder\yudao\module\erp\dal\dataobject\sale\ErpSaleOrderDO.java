package cn.iocoder.yudao.module.erp.dal.dataobject.sale;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import cn.iocoder.yudao.module.erp.dal.dataobject.finance.ErpAccountDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * ERP 销售订单 DO
 *
 * <AUTHOR>
 */
@TableName(value = "erp_sale_order")
@KeySequence("erp_sale_order_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ErpSaleOrderDO extends BaseDO {

    /**
     * 编号
     */
    @TableId
    private Long id;
    /**
     * 销售订单号
     */
    private String no;
    /**
     * 销售状态
     *
     * 枚举 {@link cn.iocoder.yudao.module.erp.enums.ErpAuditStatus}
     */
    private Integer status;
    /**
     * 客户编号
     *
     * 关联 {@link ErpCustomerDO#getId()}
     */
    private Long customerId;
    /**
     * 结算账户编号
     *
     * 关联 {@link ErpAccountDO#getId()}
     */
    private Long accountId;
    /**
     * 销售员编号
     *
     * 关联 AdminUserDO 的 id 字段
     */
    private Long saleUserId;
    /**
     * 下单时间
     */
    private LocalDateTime orderTime;

    /**
     * 合计数量
     */
    private BigDecimal totalCount;
    /**
     * 最终合计价格，单位：元
     *
     * totalPrice = totalProductPrice + totalTaxPrice - discountPrice
     */
    private BigDecimal totalPrice;

    /**
     * 合计产品价格，单位：元
     */
    private BigDecimal totalProductPrice;
    /**
     * 合计税额，单位：元
     */
    private BigDecimal totalTaxPrice;
    /**
     * 优惠率，百分比
     */
    private BigDecimal discountPercent;
    /**
     * 优惠金额，单位：元
     *
     * discountPrice = (totalProductPrice + totalTaxPrice) * discountPercent
     */
    private BigDecimal discountPrice;
    /**
     * 定金金额，单位：元
     */
    private BigDecimal depositPrice;

    /**
     * 附件地址
     */
    private String fileUrl;
    /**
     * 备注
     */
    private String remark;

    // ========== 销售出库 ==========
    /**
     * 销售出库数量
     */
    private BigDecimal outCount;

    // ========== 销售退货（入库）） ==========
    /**
     * 销售退货数量
     */
    private BigDecimal returnCount;

    // ========== 新增ERP扩展字段 ==========
    /**
     * ERP订单号
     */
    private String orderId;
    /**
     * 排料完成时间
     */
    private String createdAt;
    /**
     * 交货日期
     */
    private String dueDate;
    /**
     * 生产计划号
     */
    private String orderPno;
    /**
     * 订单来源系统
     */
    private String ppFrom;
    /**
     * 订单唯一主键(来源系统)
     */
    private String thirdUid;
    /**
     * ERP销售订单号
     */
    private String orderNo;
    /**
     * 下单日期
     */
    private String orderDate;
    /**
     * 订单数量
     */
    private Integer orderTotal;
    /**
     * 业务员
     */
    private String mrchds;
    /**
     * 指定车间
     */
    private String wkspcd;
    /**
     * ERP订单状态
     */
    private String erpStatus;
    /**
     * 款式季节
     */
    private String season;
    /**
     * 纸样文件URL
     */
    private String patternFileUrl;
    /**
     * 款式编码
     */
    private String styleCode;
    /**
     * 附加内容
     */
    private String extra;
    /**
     * 版型数组
     */
    private String stylePatterns;
    /**
     * 尺码表
     */
    private String sizes;
    /**
     * 物料用量表
     */
    private String dosages;
    /**
     * 排料结果
     */
    private String materialMarkers;
    /**
     * 毛裁尺寸表
     */
    private String mcSizes;
    /**
     * 齐码工艺尺寸表
     */
    private String fullSizeTechs;
    /**
     * 生产工艺表
     */
    private String technologies;
    /**
     * 撤销操作人
     */
    private String modifyBy;
    /**
     * 撤销原因
     */
    private String modifyReason;

}