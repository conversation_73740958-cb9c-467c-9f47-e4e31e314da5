package cn.iocoder.yudao.module.erp.dal.dataobject.sale;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import cn.iocoder.yudao.module.erp.dal.dataobject.product.ErpProductDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.math.BigDecimal;

/**
 * ERP 销售订单项 DO
 *
 * <AUTHOR>
 */
@TableName("erp_sale_order_items")
@KeySequence("erp_sale_order_items_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ErpSaleOrderItemDO extends BaseDO {

    /**
     * 编号
     */
    @TableId
    private Long id;
    /**
     * 销售订单编号
     *
     * 关联 {@link ErpSaleOrderDO#getId()}
     */
    private Long orderId;
    /**
     * 产品编号
     *
     * 关联 {@link ErpProductDO#getId()}
     */
    private Long productId;
    /**
     * 产品单位单位
     *
     * 冗余 {@link ErpProductDO#getUnitId()}
     */
    private Long productUnitId;

    /**
     * 产品单位单价，单位：元
     */
    private BigDecimal productPrice;
    /**
     * 数量
     */
    private BigDecimal count;
    /**
     * 总价，单位：元
     *
     * totalPrice = productPrice * count
     */
    private BigDecimal totalPrice;
    /**
     * 税率，百分比
     */
    private BigDecimal taxPercent;
    /**
     * 税额，单位：元
     *
     * taxPrice = totalPrice * taxPercent
     */
    private BigDecimal taxPrice;

    /**
     * 备注
     */
    private String remark;

    // ========== 销售出库 ==========
    /**
     * 销售出库数量
     */
    private BigDecimal outCount;

    // ========== 销售退货（入库）） ==========
    /**
     * 销售退货数量
     */
    private BigDecimal returnCount;

    // ========== 新增ERP扩展字段 ==========
    /**
     * ERP数量
     */
    private Integer itemQty;
    /**
     * 尺码
     */
    private String sizeNo;
    /**
     * PO单号
     */
    private String orderDno;
    /**
     * 品类编号
     */
    private String prodId;
    /**
     * 款式编号
     */
    private String stylId;
    /**
     * 主料(面料)编号
     */
    private String mainMid;
    /**
     * 主料(面料)颜色
     */
    private String mainMcolor;
    /**
     * 主料(面料)名称
     */
    private String mainMname;
    /**
     * 主料(面料)规格
     */
    private String mainSpec;
    /**
     * 品类名称
     */
    private String prodName;
    /**
     * 款式名称
     */
    private String stylName;
    /**
     * 产品类型
     */
    private String progtype;
    /**
     * 产品名称
     */
    private String skuName;
    /**
     * 产品规格
     */
    private String skuSpec;
    /**
     * 成品尺寸
     */
    private String finishedSize;
    /**
     * 单位
     */
    private String unitnm;
    /**
     * 消费者地址
     */
    private String cusadr;
    /**
     * 消费者身高
     */
    private String cushgt;
    /**
     * 消费者加款唯一ID
     */
    private String cusicd;
    /**
     * 消费者名称
     */
    private String cusnam;
    /**
     * 消费者编号
     */
    private String cusncd;
    /**
     * 消费者性别
     */
    private String cussex;
    /**
     * 消费者电话
     */
    private String custel;
    /**
     * 消费者体重
     */
    private String cuswgt;
    /**
     * 消费者部门
     */
    private String deptnm;
    /**
     * 量体师名称
     */
    private String msutnm;
    /**
     * 量体明细
     */
    private String surList;
    /**
     * 物料清单
     */
    private String bomList;
    /**
     * 工艺清单
     */
    private String tecList;
    /**
     * 款式文件
     */
    private String docList;

}