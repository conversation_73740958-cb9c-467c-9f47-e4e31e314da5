<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.yudao.module.erp.dal.mysql.statistics.ErpPurchaseStatisticsMapper">

    <select id="getPurchasePrice" resultType="java.math.BigDecimal">
        SELECT
            (SELECT IFNULL(SUM(total_price), 0)
             FROM erp_purchase_in
             WHERE in_time >= #{beginTime}
               <if test="endTime != null">
                   AND in_time &lt; #{endTime}
               </if>
               <if test="@cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder@getTenantId() != null">
                   AND tenant_id = ${@cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder@getTenantId()}
               </if>
               AND deleted = 0) -
            (SELECT IFNULL(SUM(total_price), 0)
                FROM erp_purchase_return
                WHERE return_time >= #{beginTime}
                <if test="endTime != null">
                    AND return_time &lt; #{endTime}
                </if>
                <if test="@cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder@getTenantId() != null">
                    AND tenant_id = ${@cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder@getTenantId()}
                </if>
                AND deleted = 0)
    </select>

</mapper>