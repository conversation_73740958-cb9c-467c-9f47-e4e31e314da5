#set ($subTable = $subTables.get($subIndex))##当前表
#set ($subColumns = $subColumnsList.get($subIndex))##当前字段数组
#set ($subJoinColumn = $subJoinColumns.get($subIndex))##当前 join 字段
#set ($subSimpleClassName = $subSimpleClassNames.get($subIndex))
#set ($subJoinColumn = $subJoinColumns.get($subIndex))##当前 join 字段
#set ($SubJoinColumnName = $subJoinColumn.javaField.substring(0,1).toUpperCase() + ${subJoinColumn.javaField.substring(1)})##首字母大写
<template>
  <div class="app-container">
    #if ( $subTable.subJoinMany )## 情况一：一对多，table + form
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        v-loading="formLoading"
        label-width="0px"
        :inline-message="true"
      >
        <el-table :data="formData" class="-mt-10px">
          <el-table-column label="序号" type="index" width="100" />
            #foreach($column in $subColumns)
                #if ($column.createOperation || $column.updateOperation)
                    #set ($dictType = $column.dictType)
                    #set ($javaField = $column.javaField)
                    #set ($javaType = $column.javaType)
                    #set ($AttrName = $column.javaField.substring(0,1).toUpperCase() + ${column.javaField.substring(1)})
                    #set ($comment = $column.columnComment)
                    #if ( $column.id == $subJoinColumn.id) ## 特殊：忽略主子表的 join 字段，不用填写
                    #elseif ($column.htmlType == "input" && !$column.primaryKey)## 忽略主键，不用在表单里
                      <el-table-column label="${comment}" min-width="150">
                        <template v-slot="{ row, $index }">
                          <el-form-item :prop="`${$index}.${javaField}`" :rules="formRules.${javaField}" class="mb-0px!">
                            <el-input v-model="row.${javaField}" placeholder="请输入${comment}" />
                          </el-form-item>
                        </template>
                      </el-table-column>
                    #elseif($column.htmlType == "imageUpload")## 图片上传
                        #set ($hasImageUploadColumn = true)
                      <el-table-column label="${comment}" min-width="200">
                        <template v-slot="{ row, $index }">
                          <el-form-item :prop="`${$index}.${javaField}`" :rules="formRules.${javaField}" class="mb-0px!">
                            <ImageUpload v-model="row.${javaField}"/>
                          </el-form-item>
                        </template>
                      </el-table-column>
                    #elseif($column.htmlType == "fileUpload")## 文件上传
                        #set ($hasFileUploadColumn = true)
                      <el-table-column label="${comment}" min-width="200">
                        <template v-slot="{ row, $index }">
                          <el-form-item :prop="`${$index}.${javaField}`" :rules="formRules.${javaField}" class="mb-0px!">
                            <FileUpload v-model="row.${javaField}"/>
                          </el-form-item>
                        </template>
                      </el-table-column>
                    #elseif($column.htmlType == "editor")## 文本编辑器
                        #set ($hasEditorColumn = true)
                      <el-table-column label="${comment}" min-width="400">
                        <template v-slot="{ row, $index }">
                          <el-form-item :prop="`${$index}.${javaField}`" :rules="formRules.${javaField}" class="mb-0px!">
                            <Editor v-model="row.${javaField}" :min-height="192"/>
                          </el-form-item>
                        </template>
                      </el-table-column>
                    #elseif($column.htmlType == "select")## 下拉框
                      <el-table-column label="${comment}" min-width="150">
                        <template v-slot="{ row, $index }">
                          <el-form-item :prop="`${$index}.${javaField}`" :rules="formRules.${javaField}" class="mb-0px!">
                            <el-select v-model="row.${javaField}" placeholder="请选择${comment}">
                                #if ("" != $dictType)## 有数据字典
                                  <el-option v-for="dict in this.getDictDatas(DICT_TYPE.$dictType.toUpperCase())"
                                             :key="dict.value" :label="dict.label" #if ($column.javaType == "Integer" || $column.javaType == "Long"):value="parseInt(dict.value)"#else:value="dict.value"#end />
                                #else##没数据字典
                                  <el-option label="请选择字典生成" value="" />
                                #end
                            </el-select>
                          </el-form-item>
                        </template>
                      </el-table-column>
                    #elseif($column.htmlType == "checkbox")## 多选框
                      <el-table-column label="${comment}" min-width="150">
                        <template v-slot="{ row, $index }">
                          <el-form-item :prop="`${$index}.${javaField}`" :rules="formRules.${javaField}" class="mb-0px!">
                            <el-checkbox-group v-model="row.${javaField}">
                                #if ("" != $dictType)## 有数据字典
                                  <el-checkbox v-for="dict in this.getDictDatas(DICT_TYPE.$dictType.toUpperCase())"
                                               :key="dict.value" #if($column.javaType == "Integer" || $column.javaType == "Long"):label="parseInt(dict.value)"#else:label="dict.value"#end>{{dict.label}}</el-checkbox>
                                #else##没数据字典
                                  <el-checkbox>请选择字典生成</el-checkbox>
                                #end
                            </el-checkbox-group>
                          </el-form-item>
                        </template>
                      </el-table-column>
                    #elseif($column.htmlType == "radio")## 单选框
                      <el-table-column label="${comment}" min-width="150">
                        <template v-slot="{ row, $index }">
                          <el-form-item :prop="`${$index}.${javaField}`" :rules="formRules.${javaField}" class="mb-0px!">
                            <el-radio-group v-model="row.${javaField}">
                                #if ("" != $dictType)## 有数据字典
                                  <el-radio v-for="dict in this.getDictDatas(DICT_TYPE.$dictType.toUpperCase())"
                                            :key="dict.value" #if($column.javaType == "Integer" || $column.javaType == "Long"):label="parseInt(dict.value)"
                                            #else:label="dict.value"#end>{{dict.label}}</el-radio>
                                #else##没数据字典
                                  <el-radio label="1">请选择字典生成</el-radio>
                                #end
                            </el-radio-group>
                          </el-form-item>
                        </template>
                      </el-table-column>
                    #elseif($column.htmlType == "datetime")## 时间框
                      <el-table-column label="${comment}" min-width="150">
                        <template v-slot="{ row, $index }">
                          <el-form-item :prop="`${$index}.${javaField}`" :rules="formRules.${javaField}" class="mb-0px!">
                            <el-date-picker clearable v-model="row.${javaField}" type="date" value-format="timestamp" placeholder="选择${comment}" />
                          </el-form-item>
                        </template>
                      </el-table-column>
                    #elseif($column.htmlType == "textarea")## 文本框
                      <el-table-column label="${comment}" min-width="200">
                        <template v-slot="{ row, $index }">
                          <el-form-item :prop="`${$index}.${javaField}`" :rules="formRules.${javaField}" class="mb-0px!">
                            <el-input v-model="row.${javaField}" type="textarea" placeholder="请输入${comment}" />
                          </el-form-item>
                        </template>
                      </el-table-column>
                    #end
                #end
            #end
          <el-table-column align="center" fixed="right" label="操作" width="60">
            <template v-slot="{ $index }">
              <el-link @click="handleDelete($index)">—</el-link>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <el-row justify="center" class="mt-3">
        <el-button @click="handleAdd" round>+ 添加${subTable.classComment}</el-button>
      </el-row>
    #else## 情况二：一对一，form
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="100px"
        v-loading="formLoading"
      >
          #foreach($column in $subColumns)
              #if ($column.createOperation || $column.updateOperation)
                  #set ($dictType = $column.dictType)
                  #set ($javaField = $column.javaField)
                  #set ($javaType = $column.javaType)
                  #set ($AttrName = $column.javaField.substring(0,1).toUpperCase() + ${column.javaField.substring(1)})
                  #set ($comment = $column.columnComment)
                  #if ( $column.id == $subJoinColumn.id) ## 特殊：忽略主子表的 join 字段，不用填写
                  #elseif ($column.htmlType == "input" && !$column.primaryKey)
                    <el-form-item label="${comment}" prop="${javaField}">
                      <el-input v-model="formData.${javaField}" placeholder="请输入${comment}" />
                    </el-form-item>
                  #elseif($column.htmlType == "imageUpload")## 图片上传
                      #set ($hasImageUploadColumn = true)
                    <el-form-item label="${comment}">
                      <ImageUpload v-model="formData.${javaField}"/>
                    </el-form-item>
                  #elseif($column.htmlType == "fileUpload")## 文件上传
                      #set ($hasFileUploadColumn = true)
                    <el-form-item label="${comment}">
                      <FileUpload v-model="formData.${javaField}"/>
                    </el-form-item>
                  #elseif($column.htmlType == "editor")## 文本编辑器
                      #set ($hasEditorColumn = true)
                    <el-form-item label="${comment}">
                      <Editor v-model="formData.${javaField}" :min-height="192"/>
                    </el-form-item>
                  #elseif($column.htmlType == "select")## 下拉框
                    <el-form-item label="${comment}" prop="${javaField}">
                      <el-select v-model="formData.${javaField}" placeholder="请选择${comment}">
                          #if ("" != $dictType)## 有数据字典
                            <el-option v-for="dict in this.getDictDatas(DICT_TYPE.$dictType.toUpperCase())"
                                       :key="dict.value" :label="dict.label" #if ($column.javaType == "Integer" || $column.javaType == "Long"):value="parseInt(dict.value)"#else:value="dict.value"#end />
                          #else##没数据字典
                            <el-option label="请选择字典生成" value="" />
                          #end
                      </el-select>
                    </el-form-item>
                  #elseif($column.htmlType == "checkbox")## 多选框
                    <el-form-item label="${comment}" prop="${javaField}">
                      <el-checkbox-group v-model="formData.${javaField}">
                          #if ("" != $dictType)## 有数据字典
                            <el-checkbox v-for="dict in this.getDictDatas(DICT_TYPE.$dictType.toUpperCase())"
                                         :key="dict.value" #if($column.javaType == "Integer" || $column.javaType == "Long"):label="parseInt(dict.value)"#else:label="dict.value"#end>{{dict.label}}</el-checkbox>
                          #else##没数据字典
                            <el-checkbox>请选择字典生成</el-checkbox>
                          #end
                      </el-checkbox-group>
                    </el-form-item>
                  #elseif($column.htmlType == "radio")## 单选框
                    <el-form-item label="${comment}" prop="${javaField}">
                      <el-radio-group v-model="formData.${javaField}">
                          #if ("" != $dictType)## 有数据字典
                            <el-radio v-for="dict in this.getDictDatas(DICT_TYPE.$dictType.toUpperCase())"
                                      :key="dict.value" #if($column.javaType == "Integer" || $column.javaType == "Long"):label="parseInt(dict.value)"
                                      #else:label="dict.value"#end>{{dict.label}}</el-radio>
                          #else##没数据字典
                            <el-radio label="1">请选择字典生成</el-radio>
                          #end
                      </el-radio-group>
                    </el-form-item>
                  #elseif($column.htmlType == "datetime")## 时间框
                    <el-form-item label="${comment}" prop="${javaField}">
                      <el-date-picker clearable v-model="formData.${javaField}" type="date" value-format="timestamp" placeholder="选择${comment}" />
                    </el-form-item>
                  #elseif($column.htmlType == "textarea")## 文本框
                    <el-form-item label="${comment}" prop="${javaField}">
                      <el-input v-model="formData.${javaField}" type="textarea" placeholder="请输入${comment}" />
                    </el-form-item>
                  #end
              #end
          #end
      </el-form>
    #end
  </div>
</template>

<script>
  import * as ${simpleClassName}Api from '@/api/${table.moduleName}/${table.businessName}';
      #if ($hasImageUploadColumn)
      import ImageUpload from '@/components/ImageUpload';
      #end
      #if ($hasFileUploadColumn)
      import FileUpload from '@/components/FileUpload';
      #end
      #if ($hasEditorColumn)
      import Editor from '@/components/Editor';
      #end
  export default {
    name: "${subSimpleClassName}Form",
    components: {
        #if ($hasImageUploadColumn)
          ImageUpload,
        #end
        #if ($hasFileUploadColumn)
          FileUpload,
        #end
        #if ($hasEditorColumn)
          Editor,
        #end
    },
    props:[
      '${subJoinColumn.javaField}'
    ],// ${subJoinColumn.columnComment}（主表的关联字段）
    data() {
      return {
        // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
        formLoading: false,
        // 表单参数
        formData: [],
        // 表单校验
        formRules: {
            #foreach ($column in $subColumns)
                #if (($column.createOperation || $column.updateOperation) && !$column.nullable && !${column.primaryKey})## 创建或者更新操作 && 要求非空 && 非主键
                    #set($comment=$column.columnComment)
                        $column.javaField: [{ required: true, message: "${comment}不能为空", trigger: #if($column.htmlType == "select")"change"#else"blur"#end }],
                #end
            #end
        },
      };
    },
    watch:{/** 监听主表的关联字段的变化，加载对应的子表数据 */
      ${subJoinColumn.javaField}:{
        handler(val) {
          // 1. 重置表单
            #if ( $subTable.subJoinMany )
              this.formData = []
            #else
              this.formData = {
                  #foreach ($column in $subColumns)
                      #if ($column.createOperation || $column.updateOperation)
                          #if ($column.htmlType == "checkbox")
                                  $column.javaField: [],
                          #else
                                  $column.javaField: undefined,
                          #end
                      #end
                  #end
              }
            #end
          // 2. val 非空，则加载数据
          if (!val) {
            return;
          }
          try {
            this.formLoading = true;
            // 这里还是需要获取一下 this 的不然取不到 formData
            const that = this;
            #if ( $subTable.subJoinMany )
            ${simpleClassName}Api.get${subSimpleClassName}ListBy${SubJoinColumnName}(val).then(function (res){
              that.formData = res.data;
            })
            #else
            ${simpleClassName}Api.get${subSimpleClassName}By${SubJoinColumnName}(val).then(function (res){
              const data = res.data;
              if (!data) {
                return
              }
              that.formData = data;
            })
            #end
          } finally {
            this.formLoading = false;
          }
        },
        immediate: true
      }
    },
    methods: {
        #if ( $subTable.subJoinMany )
          /** 新增按钮操作 */
          handleAdd() {
            const row = {
                #foreach ($column in $subColumns)
                    #if ($column.createOperation || $column.updateOperation)
                        #if ($column.htmlType == "checkbox")
                                $column.javaField: [],
                        #else
                                $column.javaField: undefined,
                        #end
                    #end
                #end
            }
            row.${subJoinColumn.javaField} = this.${subJoinColumn.javaField};
            this.formData.push(row);
          },
          /** 删除按钮操作 */
          handleDelete(index) {
            this.formData.splice(index, 1);
          },
        #end
      /** 表单校验 */
      validate(){
        return this.#[[$]]#refs["formRef"].validate();
      },
      /** 表单值 */
      getData(){
        return this.formData;
      }
    }
  };
</script>
