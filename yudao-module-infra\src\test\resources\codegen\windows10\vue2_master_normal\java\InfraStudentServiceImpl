package cn.iocoder.yudao.module.infra.service.demo;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import cn.iocoder.yudao.module.infra.controller.admin.demo.vo.*;
import cn.iocoder.yudao.module.infra.dal.dataobject.demo.InfraStudentDO;
import cn.iocoder.yudao.module.infra.dal.dataobject.demo.InfraStudentContactDO;
import cn.iocoder.yudao.module.infra.dal.dataobject.demo.InfraStudentTeacherDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;

import cn.iocoder.yudao.module.infra.dal.mysql.demo.InfraStudentMapper;
import cn.iocoder.yudao.module.infra.dal.mysql.demo.InfraStudentContactMapper;
import cn.iocoder.yudao.module.infra.dal.mysql.demo.InfraStudentTeacherMapper;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.infra.enums.ErrorCodeConstants.*;

/**
 * 学生 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class InfraStudentServiceImpl implements InfraStudentService {

    @Resource
    private InfraStudentMapper studentMapper;
    @Resource
    private InfraStudentContactMapper studentContactMapper;
    @Resource
    private InfraStudentTeacherMapper studentTeacherMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createStudent(InfraStudentSaveReqVO createReqVO) {
        // 插入
        InfraStudentDO student = BeanUtils.toBean(createReqVO, InfraStudentDO.class);
        studentMapper.insert(student);

        // 插入子表
        createStudentContactList(student.getId(), createReqVO.getStudentContacts());
        createStudentTeacher(student.getId(), createReqVO.getStudentTeacher());
        // 返回
        return student.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStudent(InfraStudentSaveReqVO updateReqVO) {
        // 校验存在
        validateStudentExists(updateReqVO.getId());
        // 更新
        InfraStudentDO updateObj = BeanUtils.toBean(updateReqVO, InfraStudentDO.class);
        studentMapper.updateById(updateObj);

        // 更新子表
        updateStudentContactList(updateReqVO.getId(), updateReqVO.getStudentContacts());
        updateStudentTeacher(updateReqVO.getId(), updateReqVO.getStudentTeacher());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteStudent(Long id) {
        // 校验存在
        validateStudentExists(id);
        // 删除
        studentMapper.deleteById(id);

        // 删除子表
        deleteStudentContactByStudentId(id);
        deleteStudentTeacherByStudentId(id);
    }

    private void validateStudentExists(Long id) {
        if (studentMapper.selectById(id) == null) {
            throw exception(STUDENT_NOT_EXISTS);
        }
    }

    @Override
    public InfraStudentDO getStudent(Long id) {
        return studentMapper.selectById(id);
    }

    @Override
    public PageResult<InfraStudentDO> getStudentPage(InfraStudentPageReqVO pageReqVO) {
        return studentMapper.selectPage(pageReqVO);
    }

    // ==================== 子表（学生联系人） ====================

    @Override
    public List<InfraStudentContactDO> getStudentContactListByStudentId(Long studentId) {
        return studentContactMapper.selectListByStudentId(studentId);
    }

    private void createStudentContactList(Long studentId, List<InfraStudentContactDO> list) {
        list.forEach(o -> o.setStudentId(studentId));
        studentContactMapper.insertBatch(list);
    }

    private void updateStudentContactList(Long studentId, List<InfraStudentContactDO> list) {
        deleteStudentContactByStudentId(studentId);
		list.forEach(o -> o.setId(null).setUpdater(null).setUpdateTime(null)); // 解决更新情况下：1）id 冲突；2）updateTime 不更新
        createStudentContactList(studentId, list);
    }

    private void deleteStudentContactByStudentId(Long studentId) {
        studentContactMapper.deleteByStudentId(studentId);
    }

    // ==================== 子表（学生班主任） ====================

    @Override
    public InfraStudentTeacherDO getStudentTeacherByStudentId(Long studentId) {
        return studentTeacherMapper.selectByStudentId(studentId);
    }

    private void createStudentTeacher(Long studentId, InfraStudentTeacherDO studentTeacher) {
        if (studentTeacher == null) {
            return;
        }
        studentTeacher.setStudentId(studentId);
        studentTeacherMapper.insert(studentTeacher);
    }

    private void updateStudentTeacher(Long studentId, InfraStudentTeacherDO studentTeacher) {
        if (studentTeacher == null) {
			return;
        }
        studentTeacher.setStudentId(studentId);
        studentTeacher.setUpdater(null).setUpdateTime(null); // 解决更新情况下：updateTime 不更新
        studentTeacherMapper.insertOrUpdate(studentTeacher);
    }

    private void deleteStudentTeacherByStudentId(Long studentId) {
        studentTeacherMapper.deleteByStudentId(studentId);
    }

}