import request from '@/config/axios'

export interface CategoryVO {
  id: number
  name: string
  parentId: number
}

// 查询分类列表
export const getCategoryList = async (params) => {
  return await request.get({ url: `/infra/category/list`, params })
}

// 查询分类详情
export const getCategory = async (id: number) => {
  return await request.get({ url: `/infra/category/get?id=` + id })
}

// 新增分类
export const createCategory = async (data: CategoryVO) => {
  return await request.post({ url: `/infra/category/create`, data })
}

// 修改分类
export const updateCategory = async (data: CategoryVO) => {
  return await request.put({ url: `/infra/category/update`, data })
}

// 删除分类
export const deleteCategory = async (id: number) => {
  return await request.delete({ url: `/infra/category/delete?id=` + id })
}

// 导出分类 Excel
export const exportCategory = async (params) => {
  return await request.download({ url: `/infra/category/export-excel`, params })
}
