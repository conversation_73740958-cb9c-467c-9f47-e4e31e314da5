<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.yudao.module.iot.dal.tdengine.IotDeviceLogMapper">

    <update id="createDeviceLogSTable">
        CREATE STABLE IF NOT EXISTS device_log (
                ts TIMESTAMP,
                id NCHAR(50),
                product_key NCHAR(50),
                device_name NCHAR(50),
                type NCHAR(50),
                identifier NCHAR(255),
                content NCHAR(1024),
                code INT,
                report_time TIMESTAMP
            ) TAGS (
                device_key NCHAR(50)
            )
    </update>

    <select id="showDeviceLogSTable" resultType="String">
        SHOW STABLES LIKE 'device_log'
    </select>

    <insert id="insert">
        INSERT INTO device_log_${deviceKey} (ts, id, product_key, device_name, type, identifier, content, code, report_time)
        USING device_log
        TAGS ('${deviceKey}')
        VALUES (
            NOW,
            #{id},
            #{productKey},
            #{deviceName},
            #{type},
            #{identifier},
            #{content},
            #{code},
            #{reportTime}
        )
    </insert>

    <select id="selectPage" resultType="cn.iocoder.yudao.module.iot.dal.dataobject.device.IotDeviceLogDO">
        SELECT ts, id, device_key, product_key, type, identifier, content, report_time
        FROM device_log_${reqVO.deviceKey}
        <where>
            <if test="reqVO.type != null and reqVO.type != ''">
                AND type = #{reqVO.type}
            </if>
            <if test="reqVO.identifier != null and reqVO.identifier != ''">
                AND identifier LIKE CONCAT('%',#{reqVO.identifier},'%')
            </if>
        </where>
        ORDER BY ts DESC
    </select>

    <select id="selectCountByCreateTime" resultType="Long">
        SELECT COUNT(*)
        FROM device_log
        <where>
            <if test="createTime != null">
                AND ts >= #{createTime}
            </if>
        </where>
    </select>

    <select id="selectDeviceLogUpCountByHour" resultType="java.util.Map">
        SELECT 
            TIMETRUNCATE(ts, 1h) as time,
            COUNT(*) as data 
        FROM 
        <choose>
            <when test="deviceKey != null and deviceKey != ''">
                device_log_${deviceKey}
            </when>
            <otherwise>
                device_log
            </otherwise>
        </choose>
        <where>
            <if test="startTime != null">
                AND ts >= #{startTime}
            </if>
            <if test="endTime != null">
                AND ts &lt;= #{endTime}
            </if>
            AND (
                 identifier IN ('online', 'offline', 'pull', 'progress', 'report', 'register', 'register_sub')
            )
        </where>
        GROUP BY TIMETRUNCATE(ts, 1h)
        ORDER BY time ASC
    </select>

    <select id="selectDeviceLogDownCountByHour" resultType="java.util.Map">
        SELECT 
            TIMETRUNCATE(ts, 1h) as time,
            COUNT(*) as data 
        FROM 
        <choose>
            <when test="deviceKey != null and deviceKey != ''">
                device_log_${deviceKey}
            </when>
            <otherwise>
                device_log
            </otherwise>
        </choose>
        <where>
            <if test="startTime != null">
                AND ts >= #{startTime}
            </if>
            <if test="endTime != null">
                AND ts &lt;= #{endTime}
            </if>
            AND identifier IN ('set', 'get', 'upgrade', 'unregister_sub', 'topology_add')
        </where>
        GROUP BY TIMETRUNCATE(ts, 1h)
        ORDER BY time ASC
    </select>

</mapper>