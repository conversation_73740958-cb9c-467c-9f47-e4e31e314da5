<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0" xsi:schemaLocation="
         http://maven.apache.org/POM/4.0.0
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>yudao-module-iot-plugins</artifactId>
        <groupId>cn.iocoder.boot</groupId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <packaging>jar</packaging>

    <artifactId>yudao-module-iot-plugin-emqx</artifactId>
    <version>1.0.0</version>

    <name>${project.artifactId}</name>
    <!-- TODO @芋艿：待整理 -->
    <description>
        物联网 插件模块 - emqx 插件
    </description>

    <properties>
        <!-- 插件相关 -->
        <plugin.id>emqx-plugin</plugin.id>
        <plugin.class>cn.iocoder.yudao.module.iot.plugin.emqx.config.IotEmqxPlugin</plugin.class>
        <plugin.version>${project.version}</plugin.version>
        <plugin.provider>yudao</plugin.provider>
        <plugin.description>${project.artifactId}-${project.version}</plugin.description>
        <plugin.dependencies/>
    </properties>

    <build>
        <plugins>
            <!-- 插件模式 zip -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-antrun-plugin</artifactId>
                <version>1.6</version>
                <executions>
                    <execution>
                        <id>unzip jar file</id>
                        <phase>package</phase>
                        <configuration>
                            <target>
                                <unzip src="target/${project.artifactId}-${project.version}.${project.packaging}"
                                       dest="target/plugin-classes"/>
                            </target>
                        </configuration>
                        <goals>
                            <goal>run</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <artifactId>maven-assembly-plugin</artifactId>
                <version>2.3</version>
                <configuration>
                    <descriptors>
                        <descriptor>
                            src/main/assembly/assembly.xml
                        </descriptor>
                    </descriptors>
                    <appendAssemblyId>false</appendAssemblyId>
                </configuration>
                <executions>
                    <execution>
                        <id>make-assembly</id>
                        <phase>package</phase>
                        <goals>
                            <goal>attached</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <!-- 插件模式 jar -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>2.4</version>
                <configuration>
                    <archive>
                        <manifestEntries>
                            <Plugin-Id>${plugin.id}</Plugin-Id>
                            <Plugin-Class>${plugin.class}</Plugin-Class>
                            <Plugin-Version>${plugin.version}</Plugin-Version>
                            <Plugin-Provider>${plugin.provider}</Plugin-Provider>
                            <Plugin-Description>${plugin.description}</Plugin-Description>
                            <Plugin-Dependencies>${plugin.dependencies}</Plugin-Dependencies>
                        </manifestEntries>
                    </archive>
                </configuration>
            </plugin>
            <!--            <plugin>-->
            <!--                <groupId>org.apache.maven.plugins</groupId>-->
            <!--                <artifactId>maven-shade-plugin</artifactId>-->
            <!--                <version>3.6.0</version>-->
            <!--                <executions>-->
            <!--                    <execution>-->
            <!--                        <phase>package</phase>-->
            <!--                        <goals>-->
            <!--                            <goal>shade</goal>-->
            <!--                        </goals>-->
            <!--                        <configuration>-->
            <!--                            <minimizeJar>true</minimizeJar>-->
            <!--                        </configuration>-->
            <!--                    </execution>-->
            <!--                </executions>-->
            <!--                <configuration>-->
            <!--                    <archive>-->
            <!--                        <manifestEntries>-->
            <!--                            <Plugin-Id>${plugin.id}</Plugin-Id>-->
            <!--                            <Plugin-Class>${plugin.class}</Plugin-Class>-->
            <!--                            <Plugin-Version>${plugin.version}</Plugin-Version>-->
            <!--                            <Plugin-Provider>${plugin.provider}</Plugin-Provider>-->
            <!--                            <Plugin-Description>${plugin.description}</Plugin-Description>-->
            <!--                            <Plugin-Dependencies>${plugin.dependencies}</Plugin-Dependencies>-->
            <!--                        </manifestEntries>-->
            <!--                    </archive>-->
            <!--                </configuration>-->
            <!--            </plugin>-->

            <!-- 独立模式 -->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring.boot.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                        <configuration>
                            <classifier>-standalone</classifier>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

        </plugins>
    </build>

    <dependencies>
        <dependency>
            <groupId>cn.iocoder.boot</groupId>
            <artifactId>yudao-module-iot-plugin-common</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- Web 相关 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <!-- 工具类相关 -->
        <dependency>
            <groupId>io.vertx</groupId>
            <artifactId>vertx-web</artifactId>
        </dependency>
        <dependency>
            <groupId>io.vertx</groupId>
            <artifactId>vertx-mqtt</artifactId>
        </dependency>
    </dependencies>
</project>