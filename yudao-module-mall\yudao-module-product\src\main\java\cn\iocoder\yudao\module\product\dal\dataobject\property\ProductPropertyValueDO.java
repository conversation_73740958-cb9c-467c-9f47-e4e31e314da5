package cn.iocoder.yudao.module.product.dal.dataobject.property;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;


/**
 * 商品属性值 DO
 *
 * <AUTHOR>
 */
@TableName("product_property_value")
@KeySequence("product_property_value_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductPropertyValueDO extends BaseDO {

    /**
     * SPU 单规格时，默认属性值 id
     */
    public static final Long ID_DEFAULT = 0L;
    /**
     * SPU 单规格时，默认属性值名字
     */
    public static final String NAME_DEFAULT = "默认";

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 属性项的编号
     *
     * 关联 {@link ProductPropertyDO#getId()}
     */
    private Long propertyId;
    /**
     * 名称
     */
    private String name;
    /**
     * 备注
     *
     */
    private String remark;

}
