<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.yudao.module.statistics.dal.mysql.trade.AfterSaleStatisticsMapper">

    <select id="selectSummaryByRefundTimeBetween"
            resultType="cn.iocoder.yudao.module.statistics.service.trade.bo.AfterSaleSummaryRespBO">
        SELECT COUNT(1)          AS afterSaleCount,
               SUM(refund_price) AS afterSaleRefundPrice
        FROM trade_after_sale
        WHERE refund_time BETWEEN #{beginTime} AND #{endTime}
          AND deleted = FALSE
    </select>

    <select id="selectCountByStatus" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM trade_after_sale
        WHERE status = #{status}
          AND deleted = FALSE
    </select>

</mapper>
