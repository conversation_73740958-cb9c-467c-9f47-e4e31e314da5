<template>
  <view class="status-switch-wrapper">
    <view class="switch-container">
      <text class="switch-label">停用</text>
      <switch 
        :checked="isEnable" 
        @change="handleChange"
        :color="switchColor"
      />
      <text class="switch-label">正常</text>
    </view>
    <view class="status-display">
      <text class="status-text" :class="statusClass">
        当前状态：{{ statusText }}
      </text>
    </view>
  </view>
</template>

<script>
export default {
  name: 'StatusSwitch',
  props: {
    // 状态值 (0=启用, 1=禁用)
    value: {
      type: Number,
      default: 0
    },
    // 开关颜色
    switchColor: {
      type: String,
      default: '#07c160'
    }
  },
  computed: {
    isEnable() {
      return this.value === 0
    },
    statusText() {
      return this.isEnable ? '正常' : '停用'
    },
    statusClass() {
      return this.isEnable ? 'text-green' : 'text-red'
    }
  },
  methods: {
    handleChange(e) {
      const newStatus = e.detail.value ? 0 : 1
      this.$emit('change', newStatus)
    }
  }
}
</script>

<style lang="scss" scoped>
.status-switch-wrapper {
  .switch-container {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    margin-bottom: 8px;
  }
  
  .switch-label {
    font-size: 12px;
    color: #666;
    min-width: 30px;
    text-align: center;
  }
  
  .status-display {
    text-align: center;
  }
  
  .status-text {
    font-size: 14px;
    font-weight: bold;
    
    &.text-green {
      color: #07c160;
    }
    
    &.text-red {
      color: #e54d42;
    }
  }
}
</style>
