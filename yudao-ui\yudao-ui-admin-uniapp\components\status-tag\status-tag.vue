<template>
  <text class="status-tag" :class="statusClass">
    {{ statusText }}
  </text>
</template>

<script>
import { getStatusText, getStatusClass } from '@/utils/constants'

export default {
  name: 'StatusTag',
  props: {
    // 状态值
    status: {
      type: Number,
      required: true
    },
    // 自定义文本映射
    textMap: {
      type: Object,
      default: () => ({})
    },
    // 自定义样式映射
    classMap: {
      type: Object,
      default: () => ({})
    }
  },
  computed: {
    statusText() {
      // 优先使用自定义文本映射
      if (this.textMap[this.status]) {
        return this.textMap[this.status]
      }
      return getStatusText(this.status)
    },
    statusClass() {
      // 优先使用自定义样式映射
      if (this.classMap[this.status]) {
        return this.classMap[this.status]
      }
      return getStatusClass(this.status)
    }
  }
}
</script>

<style lang="scss" scoped>
.status-tag {
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 4px;
  
  &.text-green {
    color: #67C23A;
    background-color: #f0f9ff;
    border: 1px solid #67C23A;
  }
  
  &.text-red {
    color: #F56C6C;
    background-color: #fef0f0;
    border: 1px solid #F56C6C;
  }
}
</style>
