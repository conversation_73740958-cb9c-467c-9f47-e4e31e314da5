<template>
  <view class="tree-select">
    <view class="tree-select-input" @click="showPicker">
      <text class="input-text" :class="{ placeholder: !selectedText }">
        {{ selectedText || placeholder }}
      </text>
      <uni-icons type="arrowdown" size="16" color="#999" />
    </view>
    
    <!-- 树状选择弹窗 -->
    <uni-popup ref="popup" type="bottom" :safe-area="false">
      <view class="tree-popup">
        <view class="popup-header">
          <text class="header-title">{{ title }}</text>
          <view class="header-actions">
            <button class="cu-btn sm bg-grey" @click="cancel">取消</button>
            <button class="cu-btn sm bg-blue margin-left-sm" @click="confirm">确定</button>
          </view>
        </view>
        
        <view class="tree-content">
          <scroll-view scroll-y class="tree-scroll">
            <view class="tree-node" v-for="node in treeData" :key="node.id">
              <tree-node 
                :node="node" 
                :selected-id="tempSelectedId"
                :level="0"
                @select="handleNodeSelect"
              />
            </view>
          </scroll-view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
export default {
  name: 'TreeSelect',
  components: {
    TreeNode: {
      name: 'TreeNode',
      props: {
        node: Object,
        selectedId: [String, Number],
        level: Number
      },
      data() {
        return {
          expanded: this.level === 0
        }
      },
      template: `
        <view class="node-item">
          <view 
            class="node-content" 
            :class="{ selected: node.id === selectedId }"
            :style="{ paddingLeft: (level * 20 + 10) + 'px' }"
            @click="$emit('select', node)"
          >
            <uni-icons 
              v-if="node.children && node.children.length > 0"
              :type="expanded ? 'arrowdown' : 'arrowright'" 
              size="14" 
              color="#666"
              @click.stop="toggleExpand"
            />
            <view v-else class="icon-placeholder"></view>
            <text class="node-text">{{ node.name }}</text>
          </view>
          
          <view v-if="expanded && node.children && node.children.length > 0">
            <tree-node 
              v-for="child in node.children" 
              :key="child.id"
              :node="child"
              :selected-id="selectedId"
              :level="level + 1"
              @select="$emit('select', $event)"
            />
          </view>
        </view>
      `,
      methods: {
        toggleExpand() {
          this.expanded = !this.expanded
        }
      }
    }
  },
  props: {
    value: {
      type: [String, Number],
      default: ''
    },
    placeholder: {
      type: String,
      default: '请选择'
    },
    title: {
      type: String,
      default: '选择'
    },
    options: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      tempSelectedId: null,
      treeData: []
    }
  },
  computed: {
    selectedText() {
      if (!this.value) return ''
      const node = this.findNodeById(this.treeData, this.value)
      return node ? node.name : ''
    }
  },
  watch: {
    options: {
      handler(newVal) {
        this.treeData = JSON.parse(JSON.stringify(newVal))
      },
      immediate: true,
      deep: true
    },
    value: {
      handler(newVal) {
        this.tempSelectedId = newVal
      },
      immediate: true
    }
  },
  methods: {
    findNodeById(nodes, id) {
      for (let node of nodes) {
        if (node.id === id) {
          return node
        }
        if (node.children && node.children.length > 0) {
          const found = this.findNodeById(node.children, id)
          if (found) return found
        }
      }
      return null
    },
    
    showPicker() {
      this.tempSelectedId = this.value
      this.$refs.popup.open()
    },
    
    handleNodeSelect(node) {
      this.tempSelectedId = node.id
    },
    
    confirm() {
      this.$emit('input', this.tempSelectedId)
      this.$emit('change', this.tempSelectedId)
      this.$refs.popup.close()
    },
    
    cancel() {
      this.tempSelectedId = this.value
      this.$refs.popup.close()
    }
  }
}
</script>

<style lang="scss" scoped>
.tree-select {
  .tree-select-input {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: #fff;
    
    .input-text {
      flex: 1;
      font-size: 14px;
      color: #333;
      
      &.placeholder {
        color: #999;
      }
    }
  }
}

.tree-popup {
  background-color: #fff;
  border-radius: 8px 8px 0 0;
  max-height: 70vh;
  
  .popup-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 20px;
    border-bottom: 1px solid #eee;
    
    .header-title {
      font-size: 16px;
      font-weight: bold;
      color: #333;
    }
  }
  
  .tree-content {
    height: 400px;
    
    .tree-scroll {
      height: 100%;
    }
  }
}

.node-item {
  .node-content {
    display: flex;
    align-items: center;
    padding: 12px 10px;
    border-bottom: 1px solid #f5f5f5;
    
    &.selected {
      background-color: #e6f7ff;
      color: #1890ff;
    }
    
    .icon-placeholder {
      width: 14px;
      margin-right: 8px;
    }
    
    .node-text {
      margin-left: 8px;
      font-size: 14px;
    }
  }
}

.margin-left-sm {
  margin-left: 8px;
}
</style>
