<template>
  <view class="container">
    <!-- 搜索区域 -->
    <view class="search-box">
      <uni-easyinput v-model="queryParams.name" placeholder="请输入部门名称" />
      <button class="cu-btn bg-blue" @click="handleQuery">搜索</button>
      <button class="cu-btn bg-green margin-left" @click="handleAdd">新增</button>
    </view>

    <!-- 部门列表 -->
    <view class="dept-list">
      <view v-for="(dept, index) in deptTreeData" :key="index" class="dept-item-wrapper">
        <!-- 渲染部门项 -->
        <view class="dept-item" :style="{ paddingLeft: '15px' }">
          <view class="dept-header" @click="toggleCollapse(dept.id)">
            <view class="dept-icon">
              <uni-icons v-if="dept.children && dept.children.length" :type="!collapsed.includes(dept.id) ? 'arrow-down' : 'arrow-right'" size="18"></uni-icons>
              <view v-else class="icon-placeholder"></view>
            </view>
            <view class="dept-content">
              <view class="dept-title">{{ dept.name }}</view>
              <view class="dept-info">
                <text class="dept-leader" v-if="dept.leader">{{ dept.leader }}</text>
                <text class="dept-status" :class="dept.status === 0 ? 'text-green' : 'text-red'">
                  {{ dept.status === 0 ? '正常' : '停用' }}
                </text>
              </view>
            </view>
            <view class="dept-actions">
              <button class="cu-btn sm bg-blue" @click.stop="handleEdit(dept)">编辑</button>
              <button class="cu-btn sm bg-red margin-left-sm" @click.stop="handleDelete(dept)">删除</button>
            </view>
          </view>
        </view>
        
        <!-- 渲染子部门 -->
        <view v-if="!collapsed.includes(dept.id) && dept.children && dept.children.length">
          <view v-for="(child, childIndex) in dept.children" :key="childIndex" class="dept-item-wrapper">
            <view class="dept-item" :style="{ paddingLeft: '35px' }">
              <view class="dept-header" @click="toggleCollapse(child.id)">
                <view class="dept-icon">
                  <uni-icons v-if="child.children && child.children.length" :type="!collapsed.includes(child.id) ? 'arrow-down' : 'arrow-right'" size="18"></uni-icons>
                  <view v-else class="icon-placeholder"></view>
                </view>
                <view class="dept-content">
                  <view class="dept-title">{{ child.name }}</view>
                  <view class="dept-info">
                    <text class="dept-leader" v-if="child.leader">{{ child.leader }}</text>
                    <text class="dept-status" :class="child.status === 0 ? 'text-green' : 'text-red'">
                      {{ child.status === 0 ? '正常' : '停用' }}
                    </text>
                  </view>
                </view>
                <view class="dept-actions">
                  <button class="cu-btn sm bg-blue" @click.stop="handleEdit(child)">编辑</button>
                  <button class="cu-btn sm bg-red margin-left-sm" @click.stop="handleDelete(child)">删除</button>
                </view>
              </view>
            </view>
            
            <!-- 渲染孙部门 -->
            <view v-if="!collapsed.includes(child.id) && child.children && child.children.length">
              <view v-for="(grandChild, grandChildIndex) in child.children" :key="grandChildIndex" class="dept-item-wrapper">
                <view class="dept-item" :style="{ paddingLeft: '55px' }">
                  <view class="dept-header" @click="toggleCollapse(grandChild.id)">
                    <view class="dept-icon">
                      <uni-icons v-if="grandChild.children && grandChild.children.length" :type="!collapsed.includes(grandChild.id) ? 'arrow-down' : 'arrow-right'" size="18"></uni-icons>
                      <view v-else class="icon-placeholder"></view>
                    </view>
                    <view class="dept-content">
                      <view class="dept-title">{{ grandChild.name }}</view>
                      <view class="dept-info">
                        <text class="dept-leader" v-if="grandChild.leader">{{ grandChild.leader }}</text>
                        <text class="dept-status" :class="grandChild.status === 0 ? 'text-green' : 'text-red'">
                          {{ grandChild.status === 0 ? '正常' : '停用' }}
                        </text>
                      </view>
                    </view>
                    <view class="dept-actions">
                      <button class="cu-btn sm bg-blue" @click.stop="handleEdit(grandChild)">编辑</button>
                      <button class="cu-btn sm bg-red margin-left-sm" @click.stop="handleDelete(grandChild)">删除</button>
                    </view>
                  </view>
                </view>
                
                <!-- 渲染曾孙部门 -->
                <view v-if="!collapsed.includes(grandChild.id) && grandChild.children && grandChild.children.length">
                  <view v-for="(greatGrandChild, greatGrandChildIndex) in grandChild.children" :key="greatGrandChildIndex" class="dept-item-wrapper">
                    <view class="dept-item" :style="{ paddingLeft: '75px' }">
                      <view class="dept-header" @click="toggleCollapse(greatGrandChild.id)">
                        <view class="dept-icon">
                          <uni-icons v-if="greatGrandChild.children && greatGrandChild.children.length" :type="!collapsed.includes(greatGrandChild.id) ? 'arrow-down' : 'arrow-right'" size="18"></uni-icons>
                          <view v-else class="icon-placeholder"></view>
                        </view>
                        <view class="dept-content">
                          <view class="dept-title">{{ greatGrandChild.name }}</view>
                          <view class="dept-info">
                            <text class="dept-leader" v-if="greatGrandChild.leader">{{ greatGrandChild.leader }}</text>
                            <text class="dept-status" :class="greatGrandChild.status === 0 ? 'text-green' : 'text-red'">
                              {{ greatGrandChild.status === 0 ? '正常' : '停用' }}
                            </text>
                          </view>
                        </view>
                        <view class="dept-actions">
                          <button class="cu-btn sm bg-blue" @click.stop="handleEdit(greatGrandChild)">编辑</button>
                          <button class="cu-btn sm bg-red margin-left-sm" @click.stop="handleDelete(greatGrandChild)">删除</button>
                        </view>
                      </view>
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 部门表单弹窗 -->
    <uni-popup ref="popup" type="dialog">
      <view class="popup-dialog-box">
        <view class="popup-title">{{ operationType === 'add' ? '新增部门' : '修改部门' }}</view>
        <view class="form-content">
          <uni-forms ref="form" :model="form" validate-trigger="submit">
            <uni-forms-item label="上级部门" required>
              <uni-data-select v-model="form.parentId" :localdata="deptTreeOptions" placeholder="请选择上级部门" />
            </uni-forms-item>
            <uni-forms-item label="部门名称" required>
              <uni-easyinput v-model="form.name" placeholder="请输入部门名称" />
            </uni-forms-item>
            <uni-forms-item label="负责人">
              <uni-easyinput v-model="form.leader" placeholder="请输入负责人" />
            </uni-forms-item>
            <uni-forms-item label="联系电话">
              <uni-easyinput v-model="form.phone" placeholder="请输入联系电话" />
            </uni-forms-item>
            <uni-forms-item label="邮箱">
              <uni-easyinput v-model="form.email" placeholder="请输入邮箱" />
            </uni-forms-item>
            <uni-forms-item label="部门排序" required>
              <uni-easyinput v-model="form.sort" type="number" placeholder="请输入部门排序" />
            </uni-forms-item>
            <uni-forms-item label="部门状态">
              <view class="status-switch-container">
                <switch :checked="form.status === 0" @change="handleStatusChange" />
                <text class="status-text" :class="form.status === 0 ? 'text-green' : 'text-red'">
                  {{ form.status === 0 ? '正常' : '停用' }}
                </text>
              </view>
            </uni-forms-item>
          </uni-forms>
        </view>
        <view class="popup-footer">
          <button class="cu-btn bg-gray" @click="cancelForm">取消</button>
          <button class="cu-btn bg-blue margin-left" @click="confirmForm">确定</button>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
  import { listDept, getDept, addDept, updateDept, delDept, deptTreeSelect, getSimpleDeptList } from "@/api/system/dept"

  export default {
    data() {
      return {
        // 查询参数
        queryParams: {
          name: '',
          status: undefined
        },
        // 部门列表
        deptList: [],
        // 部门树数据
        deptTreeData: [],
        // 部门树选项
        deptOptions: [],
        // 部门树选项（用于下拉框）
        deptTreeOptions: [],
        // 表单参数
        form: {
          id: undefined,
          parentId: undefined,
          name: '',
          leader: '',
          phone: '',
          email: '',
          sort: 0,
          status: 0
        },
        // 操作类型（add/edit）
        operationType: '',
        // 折叠状态
        collapsed: []
      }
    },
    onLoad() {
      this.getList()
    },
    methods: {
      // 获取部门列表
      getList() {
        this.$modal.loading("加载中...")
        listDept(this.queryParams).then(res => {
          this.deptList = res.data
          // 构建树形结构
          this.deptTreeData = this.buildDeptTree(this.deptList)
          this.$modal.closeLoading()
        }).catch(() => {
          this.$modal.closeLoading()
        })
      },
      // 构建部门树
      buildDeptTree(deptList) {
        // 先找出所有顶级部门
        const rootDepts = deptList.filter(item => item.parentId === 0)
        
        // 递归构建树结构
        const buildTree = (depts, parentId) => {
          return depts
            .filter(item => item.parentId === parentId)
            .map(item => {
              // 深拷贝当前部门项
              const dept = { ...item }
              // 查找子部门
              const children = buildTree(depts, dept.id)
              if (children.length) {
                dept.children = children
              }
              return dept
            })
        }
        
        return buildTree(deptList, 0)
      },
      // 获取部门树选项（用于下拉框）
      getDeptTreeOptions() {
        return new Promise((resolve, reject) => {
          getSimpleDeptList().then(res => {
            // 构建树形结构
            const treeData = this.buildDeptTree(res.data)
            
            // 创建顶级部门选项
            const rootOption = {
              value: 0,
              text: '顶级部门'
            }
            
            // 创建部门树选项，包含顶级部门和所有部门
            this.deptTreeOptions = [rootOption].concat(this.flattenDeptTree(treeData))
            
            resolve()
          }).catch(error => {
            reject(error)
          })
        })
      },
      // 将树形结构扁平化为一维数组（用于下拉框）
      flattenDeptTree(treeData, level = 0) {
        let result = []
        treeData.forEach(item => {
          // 添加当前节点，并根据层级添加缩进
          const prefix = level > 0 ? '　'.repeat(level) + '├ ' : ''
          result.push({
            value: item.id,
            text: prefix + item.name
          })
          
          // 递归处理子节点
          if (item.children && item.children.length > 0) {
            result = result.concat(this.flattenDeptTree(item.children, level + 1))
          }
        })
        return result
      },
      // 切换折叠状态
      toggleCollapse(id) {
        const index = this.collapsed.indexOf(id)
        if (index > -1) {
          this.collapsed.splice(index, 1)
        } else {
          this.collapsed.push(id)
        }
      },
      // 搜索按钮操作
      handleQuery() {
        this.getList()
      },
      // 重置按钮操作
      resetQuery() {
        this.queryParams = {
          name: '',
          status: undefined
        }
        this.handleQuery()
      },
      // 新增按钮操作
      async handleAdd() {
        // 先获取部门树选项
        await this.getDeptTreeOptions()
        
        this.form = {
          id: undefined,
          parentId: 0, // 默认为顶级部门
          name: '',
          leader: '',
          phone: '',
          email: '',
          sort: 0,
          status: 0
        }
        this.operationType = 'add'
        this.$refs.popup.open()
      },
      // 编辑按钮操作
      async handleEdit(row) {
        // 先获取部门树选项
        await this.getDeptTreeOptions()
        
        this.$modal.loading("加载中...")
        getDept(row.id).then(res => {
          this.form = res.data
          this.operationType = 'edit'
          this.$refs.popup.open()
          this.$modal.closeLoading()
        }).catch(() => {
          this.$modal.closeLoading()
        })
      },
      // 删除按钮操作
      handleDelete(row) {
        this.$modal.confirm(`是否确认删除部门名称为"${row.name}"的数据项？`).then(() => {
          this.$modal.loading("删除中...")
          delDept(row.id).then(() => {
            this.$modal.showToast("删除成功")
            this.getList()
          }).finally(() => {
            this.$modal.closeLoading()
          })
        })
      },
      // 状态修改
      handleStatusChange(e) {
        this.form.status = e.detail.value ? 0 : 1
      },
      // 确认表单
      confirmForm() {
        // 表单校验
        if (!this.form.name) {
          this.$modal.showToast("请输入部门名称")
          return
        }
        if (!this.form.sort && this.form.sort !== 0) {
          this.$modal.showToast("请输入部门排序")
          return
        }
        
        this.$modal.loading("保存中...")
        if (this.operationType === 'add') {
          // 新增
          addDept(this.form).then(() => {
            this.$modal.showToast("新增成功")
            this.getList()
          }).finally(() => {
            this.$modal.closeLoading()
            this.$refs.popup.close()
          })
        } else {
          // 修改
          updateDept(this.form).then(() => {
            this.$modal.showToast("修改成功")
            this.getList()
          }).finally(() => {
            this.$modal.closeLoading()
            this.$refs.popup.close()
          })
        }
      },
      // 取消表单
      cancelForm() {
        this.$refs.popup.close()
      }
    }
  }
</script>

<style lang="scss">
  .container {
    padding: 10px;
  }
  
  .search-box {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    
    .uni-easyinput {
      flex: 1;
    }
    
    .cu-btn {
      margin-left: 10px;
    }
  }
  
  .dept-list {
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 6px rgba(0, 0, 0, 0.1);
    margin-bottom: 10px;
  }
  
  .dept-item-wrapper {
    border-bottom: 1px solid #f0f0f0;
    
    &:last-child {
      border-bottom: none;
    }
  }
  
  .dept-item {
    &:first-child {
      background-color: #f8f8f8;
    }
  }
  
  .dept-header {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    
    .dept-icon {
      width: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .icon-placeholder {
      width: 18px;
    }
    
    .dept-content {
      flex: 1;
      margin-left: 5px;
      
      .dept-title {
        font-size: 14px;
        color: #333;
      }
      
      .dept-info {
        display: flex;
        align-items: center;
        margin-top: 4px;
        font-size: 12px;
        color: #999;
        
        .dept-leader {
          margin-right: 10px;
        }
      }
    }
    
    .dept-actions {
      display: flex;
      align-items: center;
    }
  }
  
  .dept-title {
    font-size: 14px;
    color: #333;
    flex: 1;
  }
  
  .popup-dialog-box {
    background-color: #fff;
    border-radius: 8px;
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }
  
  .popup-title {
    font-size: 16px;
    font-weight: bold;
    padding: 15px;
    text-align: center;
    border-bottom: 1px solid #eee;
  }
  
  .form-content {
    padding: 15px;
    overflow-y: auto;
    max-height: calc(80vh - 120px);
  }
  
  .popup-footer {
    display: flex;
    justify-content: flex-end;
    padding: 10px 15px;
    border-top: 1px solid #eee;
  }
  
  .margin-left {
    margin-left: 10px;
  }
  
  .margin-left-sm {
    margin-left: 5px;
  }
  
  .text-green {
    color: #07c160;
  }
  
  .text-red {
    color: #e54d42;
  }

  .status-switch-container {
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .status-text {
    font-size: 14px;
    font-weight: bold;
  }
</style>