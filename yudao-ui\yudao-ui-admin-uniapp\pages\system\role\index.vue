<template>
  <view class="container">
    <!-- 搜索区域 -->
    <view class="search-box">
      <uni-easyinput v-model="queryParams.name" placeholder="请输入角色名称" />
      <button class="cu-btn bg-blue" @click="handleQuery">搜索</button>
      <button class="cu-btn bg-green margin-left" @click="handleAdd">新增</button>
    </view>
    
    <!-- 角色列表 -->
    <view class="role-list">
      <uni-list>
        <uni-list-item v-for="(item, index) in roleList" :key="index" :title="item.name" 
          :note="item.code" :showExtraIcon="true" :extraIcon="{type: 'staff'}" 
          clickable @click="handleRowClick(item)">
          <template v-slot:footer>
            <view class="footer-box">
              <text class="status" :class="item.status === 0 ? 'text-green' : 'text-red'">
                {{ item.status === 0 ? '正常' : '停用' }}
              </text>
              <view class="action-box">
                <button class="cu-btn sm bg-blue" @click.stop="handleEdit(item)">编辑</button>
                <button class="cu-btn sm bg-red margin-left-sm" @click.stop="handleDelete(item)">删除</button>
              </view>
            </view>
          </template>
        </uni-list-item>
      </uni-list>
    </view>
    
    <!-- 分页区域 -->
    <view class="pagination-box">
      <uni-pagination show-icon :total="total" :pageSize="queryParams.pageSize" 
        :current="queryParams.pageNo" @change="handlePageChange" />
    </view>
    
    <!-- 角色表单弹窗 -->
    <uni-popup ref="popup" type="dialog">
      <uni-popup-dialog mode="input" title="角色信息" :value="form.name" 
        placeholder="请输入角色名称" @confirm="confirmForm" @close="cancelForm">
        <view class="form-content">
          <uni-forms ref="form" :modelValue="form">
            <uni-forms-item label="角色名称" required>
              <uni-easyinput v-model="form.name" placeholder="请输入角色名称" />
            </uni-forms-item>
            <uni-forms-item label="角色编码" required>
              <uni-easyinput v-model="form.code" placeholder="请输入角色编码" />
            </uni-forms-item>
            <uni-forms-item label="角色排序" required>
              <uni-easyinput v-model="form.sort" type="number" placeholder="请输入角色排序" />
            </uni-forms-item>
            <uni-forms-item label="角色状态">
              <view class="status-switch-container">
                <switch :checked="form.status === 0" @change="handleStatusChange" />
                <text class="status-text" :class="form.status === 0 ? 'text-green' : 'text-red'">
                  {{ form.status === 0 ? '正常' : '停用' }}
                </text>
              </view>
            </uni-forms-item>
            <uni-forms-item label="备注">
              <uni-easyinput v-model="form.remark" type="textarea" placeholder="请输入备注" />
            </uni-forms-item>
          </uni-forms>
        </view>
      </uni-popup-dialog>
    </uni-popup>
  </view>
</template>

<script>
  import { listRole, getRole, addRole, updateRole, delRole, changeRoleStatus } from "@/api/system/role"

  export default {
    data() {
      return {
        // 查询参数
        queryParams: {
          pageNo: 1,
          pageSize: 10,
          name: '',
          status: undefined
        },
        // 角色列表
        roleList: [],
        // 总条数
        total: 0,
        // 表单参数
        form: {
          id: undefined,
          name: '',
          code: '',
          sort: 0,
          status: 0,
          remark: ''
        },
        // 操作类型（add/edit）
        operationType: ''
      }
    },
    onLoad() {
      this.getList()
    },
    methods: {
      // 获取角色列表
      getList() {
        this.$modal.loading("加载中...")
        listRole(this.queryParams).then(res => {
          this.roleList = res.data.list
          this.total = res.data.total
          this.$modal.closeLoading()
        }).catch(() => {
          this.$modal.closeLoading()
        })
      },
      // 搜索按钮操作
      handleQuery() {
        this.queryParams.pageNo = 1
        this.getList()
      },
      // 重置按钮操作
      resetQuery() {
        this.queryParams = {
          pageNo: 1,
          pageSize: 10,
          name: '',
          status: undefined
        }
        this.handleQuery()
      },
      // 新增按钮操作
      handleAdd() {
        this.form = {
          id: undefined,
          name: '',
          code: '',
          sort: 0,
          status: 0,
          remark: ''
        }
        this.operationType = 'add'
        this.$refs.popup.open()
      },
      // 编辑按钮操作
      handleEdit(row) {
        this.$modal.loading("加载中...")
        getRole(row.id).then(res => {
          this.form = res.data
          this.operationType = 'edit'
          this.$refs.popup.open()
          this.$modal.closeLoading()
        }).catch(() => {
          this.$modal.closeLoading()
        })
      },
      // 删除按钮操作
      handleDelete(row) {
        this.$modal.confirm(`是否确认删除角色名称为"${row.name}"的数据项？`).then(() => {
          this.$modal.loading("删除中...")
          delRole(row.id).then(() => {
            this.$modal.showToast("删除成功")
            this.getList()
          }).finally(() => {
            this.$modal.closeLoading()
          })
        })
      },
      // 行点击事件
      handleRowClick(row) {
        // 查看角色详情
        this.$tab.navigateTo(`/pages/system/role/detail?id=${row.id}`)
      },
      // 状态修改
      handleStatusChange(e) {
        console.log("角色状态开关变化:", e.detail.value)
        this.form.status = e.detail.value ? 0 : 1
        console.log("更新后的角色状态:", this.form.status)
      },
      // 确认表单
      confirmForm() {
        // 表单校验
        if (!this.form.name) {
          this.$modal.showToast("请输入角色名称")
          return
        }
        if (!this.form.code) {
          this.$modal.showToast("请输入角色编码")
          return
        }
        if (!this.form.sort && this.form.sort !== 0) {
          this.$modal.showToast("请输入角色排序")
          return
        }
        
        this.$modal.loading("保存中...")
        if (this.operationType === 'add') {
          // 新增
          addRole(this.form).then(() => {
            this.$modal.showToast("新增成功")
            this.getList()
          }).finally(() => {
            this.$modal.closeLoading()
            this.$refs.popup.close()
          })
        } else {
          // 修改
          updateRole(this.form).then(() => {
            this.$modal.showToast("修改成功")
            this.getList()
          }).finally(() => {
            this.$modal.closeLoading()
            this.$refs.popup.close()
          })
        }
      },
      // 取消表单
      cancelForm() {
        this.$refs.popup.close()
      },
      // 分页
      handlePageChange(e) {
        this.queryParams.pageNo = e.current
        this.getList()
      }
    }
  }
</script>

<style lang="scss">
  .container {
    padding: 10px;
  }
  
  .search-box {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    
    .uni-easyinput {
      flex: 1;
    }
    
    .cu-btn {
      margin-left: 10px;
    }
  }
  
  .role-list {
    margin-bottom: 10px;
  }
  
  .footer-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    
    .action-box {
      display: flex;
    }
  }
  
  .pagination-box {
    display: flex;
    justify-content: center;
    margin-top: 10px;
  }
  
  .form-content {
    padding: 10px;
  }
  
  .margin-left {
    margin-left: 10px;
  }
  
  .margin-left-sm {
    margin-left: 5px;
  }
  
  .text-green {
    color: #07c160;
  }
  
  .text-red {
    color: #e54d42;
  }

  .status-switch-container {
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .status-text {
    font-size: 14px;
    font-weight: bold;
  }
</style> 