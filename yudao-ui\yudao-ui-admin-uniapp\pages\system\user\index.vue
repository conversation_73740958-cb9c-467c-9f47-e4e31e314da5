<template>
  <view class="container">
    <!-- 搜索区域 -->
    <view class="search-box">
      <uni-easyinput v-model="queryParams.username" placeholder="请输入用户名称" />
      <button class="cu-btn bg-blue" @click="handleQuery">搜索</button>
      <button class="cu-btn bg-green margin-left" @click="handleAdd">新增</button>
      <button class="cu-btn bg-grey margin-left" @click="resetSearch">重置</button>
    </view>
    
    <!-- 面包屑导航 -->
    <view class="breadcrumb" v-if="breadcrumbs.length > 0">
      <view class="breadcrumb-item" @click="navigateToHome">
        <text class="breadcrumb-text">部门列表</text>
        <text class="breadcrumb-separator" v-if="breadcrumbs.length > 0">/</text>
      </view>
      <view v-for="(item, index) in breadcrumbs" :key="index" class="breadcrumb-item" 
            @click="navigateToBreadcrumb(index)">
        <text class="breadcrumb-text">{{ item.name }}</text>
        <text class="breadcrumb-separator" v-if="index < breadcrumbs.length - 1">/</text>
      </view>
    </view>
    
    <!-- 部门和用户列表 -->
    <view class="list-container">
      <!-- 显示部门列表 -->
      <view v-if="!showUsers" class="dept-list">
        <view v-for="(dept, index) in currentDeptList" :key="index" class="dept-item" @click="handleDeptClick(dept)">
          <view class="dept-icon">
            <uni-icons type="folder" size="20"></uni-icons>
          </view>
          <view class="dept-content">
            <view class="dept-title">{{ dept.name }}</view>
            <view class="dept-info">
              <text class="dept-leader" v-if="dept.leader">{{ dept.leader }}</text>
              <text class="dept-status" :class="dept.status === 0 ? 'text-green' : 'text-red'">
                {{ dept.status === 0 ? '正常' : '停用' }}
              </text>
            </view>
          </view>
          <view class="dept-arrow">
            <uni-icons :type="hasChildren(dept) ? 'right' : 'staff'" size="16"></uni-icons>
          </view>
        </view>
        
        <!-- 无数据提示 -->
        <view v-if="currentDeptList.length === 0" class="empty-tip">
          <text>暂无部门数据</text>
        </view>
      </view>
      
      <!-- 显示用户列表 -->
      <view v-else class="user-list">
        <view v-for="(user, index) in userList" :key="index" class="user-item">
          <view class="user-avatar">
            <image class="avatar-image" :src="user.avatar || '/static/images/avatar.png'" mode="aspectFill"></image>
          </view>
          <view class="user-content">
            <view class="user-name">{{ user.nickname || user.username }}</view>
            <view class="user-info">
              <text class="user-username">{{ user.username }}</text>
              <text class="user-status" :class="user.status === 0 ? 'text-green' : 'text-red'">
                {{ user.status === 0 ? '正常' : '停用' }}
              </text>
            </view>
          </view>
          <view class="user-actions">
            <button class="cu-btn sm bg-blue" @click.stop="handleEdit(user)">编辑</button>
            <button class="cu-btn sm bg-red margin-left-sm" @click.stop="handleDelete(user)">删除</button>
          </view>
        </view>
        
        <!-- 无数据提示 -->
        <view v-if="userList.length === 0" class="empty-tip">
          <text>该部门下暂无用户</text>
        </view>
      </view>
    </view>
    
    <!-- 分页区域 -->
    <view class="pagination-box" v-if="showUsers && total > queryParams.pageSize">
      <uni-pagination show-icon :total="total" :pageSize="queryParams.pageSize" 
        :current="queryParams.pageNo" @change="handlePageChange" />
    </view>
    
    <!-- 新增用户弹窗 -->
    <uni-popup ref="addPopup" type="dialog">
      <view class="popup-dialog-box">
        <view class="popup-title">新增用户</view>
        <view class="form-content">
          <uni-forms ref="addForm" :model="addForm" validate-trigger="submit">
            <uni-forms-item label="用户名称" required>
              <uni-easyinput v-model="addForm.username" placeholder="请输入用户名称" />
            </uni-forms-item>
            <uni-forms-item label="用户昵称" required>
              <uni-easyinput v-model="addForm.nickname" placeholder="请输入用户昵称" />
            </uni-forms-item>
            <uni-forms-item label="归属部门">
              <picker @change="handleAddDeptChange" :value="addDeptIndex" :range="deptPickerOptions" range-key="text">
                <view class="uni-input dept-picker">
                  {{ getDeptNameById(addForm.deptId) || '请选择归属部门' }}
                  <uni-icons type="arrowdown" size="14" color="#999"></uni-icons>
                </view>
              </picker>
            </uni-forms-item>
            <uni-forms-item label="手机号码">
              <uni-easyinput v-model="addForm.mobile" placeholder="请输入手机号码" />
            </uni-forms-item>
            <uni-forms-item label="邮箱">
              <uni-easyinput v-model="addForm.email" placeholder="请输入邮箱" />
            </uni-forms-item>
            <uni-forms-item label="密码" required>
              <uni-easyinput v-model="addForm.password" type="password" placeholder="请输入密码" />
            </uni-forms-item>
            <uni-forms-item label="用户状态">
              <view class="status-switch-container">
                <switch :checked="addForm.status === 0" @change="handleAddStatusChange" />
                <text class="status-text" :class="addForm.status === 0 ? 'text-green' : 'text-red'">
                  {{ addForm.status === 0 ? '正常' : '停用' }}
                </text>
              </view>
            </uni-forms-item>
          </uni-forms>
        </view>
        <view class="popup-footer">
          <button class="cu-btn bg-gray" @click="cancelAdd">取消</button>
          <button class="cu-btn bg-blue margin-left" @click="confirmAdd">确定</button>
        </view>
      </view>
    </uni-popup>
    
    <!-- 编辑用户弹窗 -->
    <uni-popup ref="editPopup" type="dialog">
      <view class="popup-dialog-box">
        <view class="popup-title">编辑用户</view>
        <view class="form-content">
          <uni-forms ref="editForm" :model="editForm" validate-trigger="submit">
            <uni-forms-item label="用户名称" required>
              <uni-easyinput v-model="editForm.username" placeholder="请输入用户名称" />
            </uni-forms-item>
            <uni-forms-item label="用户昵称" required>
              <uni-easyinput v-model="editForm.nickname" placeholder="请输入用户昵称" />
            </uni-forms-item>
            <uni-forms-item label="归属部门">
              <picker @change="handleEditDeptChange" :value="editDeptIndex" :range="deptPickerOptions" range-key="text">
                <view class="uni-input dept-picker">
                  {{ getDeptNameById(editForm.deptId) || '请选择归属部门' }}
                  <uni-icons type="arrowdown" size="14" color="#999"></uni-icons>
                </view>
              </picker>
            </uni-forms-item>
            <uni-forms-item label="手机号码">
              <uni-easyinput v-model="editForm.mobile" placeholder="请输入手机号码" />
            </uni-forms-item>
            <uni-forms-item label="邮箱">
              <uni-easyinput v-model="editForm.email" placeholder="请输入邮箱" />
            </uni-forms-item>
            <uni-forms-item label="用户状态">
              <view class="status-switch-container">
                <switch :checked="editForm.status === 0" @change="handleEditStatusChange" />
                <text class="status-text" :class="editForm.status === 0 ? 'text-green' : 'text-red'">
                  {{ editForm.status === 0 ? '正常' : '停用' }}
                </text>
              </view>
            </uni-forms-item>
          </uni-forms>
        </view>
        <view class="popup-footer">
          <button class="cu-btn bg-gray" @click="cancelEdit">取消</button>
          <button class="cu-btn bg-blue margin-left" @click="confirmEdit">确定</button>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
  import { listUser, getUser, addUser, updateUser, delUser, changeUserStatus } from "@/api/system/user"
  import { listDept, getDept } from "@/api/system/dept"

  export default {
    components: {
    },
    data() {
      return {
        // 查询参数
        queryParams: {
          pageNo: 1,
          pageSize: 10,
          username: '',
          status: undefined,
          deptId: undefined
        },
        // 部门相关
        deptTree: [],
        deptTreeOptions: [],
        deptPickerOptions: [], // 用于部门选择器的扁平化选项
        currentDeptList: [], // 当前显示的部门列表
        currentParentId: 0, // 当前父部门ID，0表示顶级部门
        breadcrumbs: [], // 面包屑导航
        showUsers: false, // 是否显示用户列表
        // 用户列表
        userList: [],
        // 总条数
        total: 0,
        // 新增表单参数
        addForm: {
          username: '',
          nickname: '',
          mobile: '',
          email: '',
          password: '',
          deptId: undefined,
          status: 0
        },
        addDeptIndex: 0, // 新增用户部门选择器索引
        // 编辑表单参数
        editForm: {
          id: undefined,
          username: '',
          nickname: '',
          mobile: '',
          email: '',
          deptId: undefined,
          status: 0
        },
        editDeptIndex: 0, // 编辑用户部门选择器索引
        // loading状态
        isLoading: false,
        // 是否是搜索状态
        isSearchMode: false
      }
    },
    onLoad() {
      this.getDeptList()
    },
    methods: {
      // 获取部门列表
      getDeptList() {
        this.showLoading("加载中...")
        listDept({}).then(res => {
          this.deptTree = res.data || []
          
          // 初始化显示一级部门
          this.currentDeptList = this.deptTree.filter(item => item.parentId === 0)
          this.currentParentId = 0
          this.breadcrumbs = []
          this.showUsers = false
          
          // 初始化部门选择器选项
          this.initDeptPickerOptions()
          
          this.hideLoading()
        }).catch(() => {
          this.deptTree = []
          this.deptPickerOptions = []
          this.hideLoading()
        })
      },
      
      // 获取用户列表
      getUserList() {
        if (this.isLoading) return
        
        this.showLoading("加载中...")
        this.queryParams.pageNo = 1
        listUser(this.queryParams).then(res => {
          this.userList = res.data.list
          this.total = res.data.total
          this.hideLoading()
        }).catch(() => {
          this.hideLoading()
        })
      },
      
      // 搜索按钮操作
      handleQuery() {
        if (!this.queryParams.username.trim()) {
          // 如果搜索内容为空且不是搜索状态，不执行搜索
          if (!this.isSearchMode) {
            return
          }
          // 如果是搜索状态但搜索内容为空，则重置搜索
          this.resetSearch()
          return
        }
        
        // 进入搜索模式
        this.isSearchMode = true
        this.showLoading("搜索中...")
        
        // 重置分页
        this.queryParams.pageNo = 1
        
        // 清除部门ID筛选，搜索所有部门
        const originalDeptId = this.queryParams.deptId
        this.queryParams.deptId = undefined
        
        // 搜索用户
        listUser(this.queryParams).then(async res => {
          if (res.data.list && res.data.list.length > 0) {
            this.userList = res.data.list
            this.total = res.data.total
            
            // 获取第一个用户的部门信息
            const firstUser = res.data.list[0]
            if (firstUser.deptId) {
              try {
                // 获取用户所在部门的详细信息
                const deptRes = await getDept(firstUser.deptId)
                const userDept = deptRes.data
                
                if (userDept) {
                  // 构建部门路径
                  await this.buildDeptPath(userDept)
                  
                  // 设置部门ID，但不重新请求用户列表（因为已经有搜索结果）
                  this.queryParams.deptId = firstUser.deptId
                  this.showUsers = true
                }
              } catch (error) {
                console.error("获取部门信息失败:", error)
              }
            } else {
              // 如果用户没有部门，直接显示搜索结果
              this.breadcrumbs = [{
                id: -1,
                name: "搜索结果",
                parentId: 0
              }]
              this.showUsers = true
            }
            
            this.$modal.showToast(`找到 ${res.data.total} 条结果`)
          } else {
            // 没有找到结果
            this.userList = []
            this.total = 0
            
            // 显示空的搜索结果页
            this.breadcrumbs = [{
              id: -1,
              name: "搜索结果",
              parentId: 0
            }]
            this.showUsers = true
            
            this.$modal.showToast("未找到匹配的用户")
          }
          
          this.hideLoading()
        }).catch(error => {
          console.error("搜索失败:", error)
          this.hideLoading()
          this.queryParams.deptId = originalDeptId // 恢复原来的部门ID
        })
      },
      
      // 重置搜索
      resetSearch() {
        // 清除搜索条件
        this.queryParams.username = ''
        this.queryParams.status = undefined
        
        // 退出搜索模式
        this.isSearchMode = false
        
        // 如果当前在用户列表页，则重新加载当前部门的用户
        if (this.showUsers && this.queryParams.deptId) {
          this.getUserList()
        } else {
          // 否则回到部门列表首页
          this.navigateToHome()
        }
      },
      
      // 构建部门路径
      async buildDeptPath(dept) {
        // 清空现有面包屑
        this.breadcrumbs = []
        
        // 临时存储部门路径
        const deptPath = []
        let currentDept = dept
        
        // 从当前部门向上查找父部门，构建完整路径
        while (currentDept) {
          // 将当前部门添加到路径开头
          deptPath.unshift({
            id: currentDept.id,
            name: currentDept.name,
            parentId: currentDept.parentId
          })
          
          // 如果是顶级部门，结束循环
          if (currentDept.parentId === 0) {
            break
          }
          
          try {
            // 获取父部门信息
            const parentRes = await getDept(currentDept.parentId)
            currentDept = parentRes.data
          } catch (error) {
            console.error("获取父部门失败:", error)
            break
          }
        }
        
        // 设置面包屑
        this.breadcrumbs = deptPath
      },
      
      // 初始化部门选择器选项
      initDeptPickerOptions() {
        // 清空选项
        this.deptPickerOptions = []
        
        // 添加顶级部门选项
        this.deptPickerOptions.push({
          value: 0,
          text: '顶级部门'
        })
        
        // 遍历一级部门
        const level1Depts = this.deptTree.filter(item => item.parentId === 0)
        level1Depts.forEach(dept1 => {
          this.deptPickerOptions.push({
            value: dept1.id,
            text: dept1.name
          })
          
          // 遍历二级部门
          const level2Depts = this.deptTree.filter(item => item.parentId === dept1.id)
          level2Depts.forEach(dept2 => {
            this.deptPickerOptions.push({
              value: dept2.id,
              text: '　├ ' + dept2.name
            })
            
            // 遍历三级部门
            const level3Depts = this.deptTree.filter(item => item.parentId === dept2.id)
            level3Depts.forEach(dept3 => {
              this.deptPickerOptions.push({
                value: dept3.id,
                text: '　　├ ' + dept3.name
              })
              
              // 遍历四级部门
              const level4Depts = this.deptTree.filter(item => item.parentId === dept3.id)
              level4Depts.forEach(dept4 => {
                this.deptPickerOptions.push({
                  value: dept4.id,
                  text: '　　　├ ' + dept4.name
                })
              })
            })
          })
        })
      },
      
      // 根据部门ID获取部门名称
      getDeptNameById(deptId) {
        if (deptId === 0) return '顶级部门'
        if (!deptId) return ''
        
        const option = this.deptPickerOptions.find(item => item.value === deptId)
        return option ? option.text : ''
      },
      
      // 根据部门ID获取选择器索引
      getDeptIndexById(deptId) {
        if (!deptId && deptId !== 0) return 0
        
        const index = this.deptPickerOptions.findIndex(item => item.value === deptId)
        return index >= 0 ? index : 0
      },
      
      // 新增用户部门选择变更
      handleAddDeptChange(e) {
        const index = e.detail.value
        this.addDeptIndex = index
        this.addForm.deptId = this.deptPickerOptions[index].value
      },
      
      // 编辑用户部门选择变更
      handleEditDeptChange(e) {
        const index = e.detail.value
        this.editDeptIndex = index
        this.editForm.deptId = this.deptPickerOptions[index].value
      },
      
      // 新增按钮操作
      handleAdd() {
        this.addForm = {
          username: '',
          nickname: '',
          mobile: '',
          email: '',
          password: '',
          deptId: this.queryParams.deptId || 0,
          status: 0
        }
        
        // 设置部门选择器索引
        this.addDeptIndex = this.getDeptIndexById(this.addForm.deptId)
        
        this.$refs.addPopup.open()
      },
      
      // 编辑按钮操作
      handleEdit(row) {
        this.showLoading("加载中...")
        getUser(row.id).then(res => {
          this.editForm = res.data
          
          // 设置部门选择器索引
          this.editDeptIndex = this.getDeptIndexById(this.editForm.deptId)
          
          this.$refs.editPopup.open()
          this.hideLoading()
        }).catch(() => {
          this.hideLoading()
        })
      },
      
      // 删除按钮操作
      handleDelete(row) {
        this.$modal.confirm(`是否确认删除用户名称为"${row.username}"的数据项？`).then(() => {
          this.showLoading("删除中...")
          delUser(row.id).then(() => {
            this.$modal.showToast("删除成功")
            if (this.showUsers) {
              this.getUserList()
            }
            this.hideLoading()
          }).catch(() => {
            this.hideLoading()
          })
        })
      },
      
      // 新增表单状态修改
      handleAddStatusChange(e) {
        this.addForm.status = e.detail.value ? 0 : 1
      },
      
      // 编辑表单状态修改
      handleEditStatusChange(e) {
        this.editForm.status = e.detail.value ? 0 : 1
      },
      
      // 确认新增
      confirmAdd() {
        // 表单校验
        if (!this.addForm.username) {
          this.$modal.showToast("请输入用户名称")
          return
        }
        if (!this.addForm.nickname) {
          this.$modal.showToast("请输入用户昵称")
          return
        }
        if (!this.addForm.password) {
          this.$modal.showToast("请输入密码")
          return
        }
        
        this.showLoading("保存中...")
        addUser(this.addForm).then(() => {
          this.$modal.showToast("新增成功")
          if (this.showUsers) {
            this.getUserList()
          }
          this.$refs.addPopup.close()
          this.hideLoading()
        }).catch(() => {
          this.hideLoading()
        })
      },
      
      // 取消新增
      cancelAdd() {
        this.$refs.addPopup.close()
      },
      
      // 确认编辑
      confirmEdit() {
        // 表单校验
        if (!this.editForm.username) {
          this.$modal.showToast("请输入用户名称")
          return
        }
        if (!this.editForm.nickname) {
          this.$modal.showToast("请输入用户昵称")
          return
        }
        
        this.showLoading("保存中...")
        updateUser(this.editForm).then(() => {
          this.$modal.showToast("修改成功")
          if (this.showUsers) {
            this.getUserList()
          }
          this.$refs.editPopup.close()
          this.hideLoading()
        }).catch(() => {
          this.hideLoading()
        })
      },
      
      // 取消编辑
      cancelEdit() {
        this.$refs.editPopup.close()
      },
      
      // 分页
      handlePageChange(e) {
        this.queryParams.pageNo = e.current
        this.getUserList()
      },
      
      // 构建树形选项（用于表单选择器）
      buildTreeOptions(data) {
        const options = [{
          id: 0,
          name: '主类目',
          children: this.listToTree(data.map(item => ({
            id: item.id,
            name: item.name,
            parentId: item.parentId
          })))
        }]
        return options
      },
      
      // 将列表转换为树形结构
      listToTree(list, parentId = 0) {
        const tree = []
        list.forEach(item => {
          if (item.parentId === parentId) {
            const children = this.listToTree(list, item.id)
            if (children.length > 0) {
              item.children = children
            }
            tree.push(item)
          }
        })
        return tree.sort((a, b) => (a.sort || 0) - (b.sort || 0))
      },
      
      // 处理部门点击
      handleDeptClick(dept) {
        // 检查是否有子部门
        const hasChildDepts = this.deptTree.some(item => item.parentId === dept.id)
        
        if (hasChildDepts) {
          // 有子部门，显示子部门列表
          this.currentDeptList = this.deptTree.filter(item => item.parentId === dept.id)
          this.currentParentId = dept.id
          this.breadcrumbs.push({
            id: dept.id,
            name: dept.name,
            parentId: dept.parentId
          })
          this.showUsers = false
        } else {
          // 没有子部门，显示该部门下的用户
          this.queryParams.deptId = dept.id
          this.breadcrumbs.push({
            id: dept.id,
            name: dept.name,
            parentId: dept.parentId
          })
          this.showUsers = true
          this.getUserList()
        }
      },
      
      // 导航到首页
      navigateToHome() {
        this.currentDeptList = this.deptTree.filter(item => item.parentId === 0)
        this.currentParentId = 0
        this.breadcrumbs = []
        this.showUsers = false
        this.queryParams.deptId = undefined
      },
      
      // 导航到面包屑
      navigateToBreadcrumb(index) {
        if (index === this.breadcrumbs.length - 1) return
        
        // 截取到指定位置的面包屑
        const targetBreadcrumb = this.breadcrumbs[index]
        this.breadcrumbs = this.breadcrumbs.slice(0, index + 1)
        
        // 更新当前部门列表
        if (targetBreadcrumb.parentId === 0) {
          // 回到一级部门
          this.currentDeptList = this.deptTree.filter(item => item.parentId === 0)
          this.currentParentId = 0
        } else {
          // 回到中间层级部门
          this.currentDeptList = this.deptTree.filter(item => item.parentId === targetBreadcrumb.id)
          this.currentParentId = targetBreadcrumb.id
        }
        
        this.showUsers = false
        this.queryParams.deptId = undefined
      },
      
      // 判断部门是否有子部门
      hasChildren(dept) {
        return this.deptTree.some(item => item.parentId === dept.id)
      },
      
      // 显示加载中
      showLoading(title) {
        if (!this.isLoading) {
          this.isLoading = true
          this.$modal.loading(title)
        }
      },
      
      // 隐藏加载中
      hideLoading() {
        if (this.isLoading) {
          this.isLoading = false
          this.$modal.closeLoading()
        }
      }
    }
  }
</script>

<style lang="scss">
  .container {
    padding: 10px;
  }
  
  .search-box {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    
    .uni-easyinput {
      flex: 1;
    }
    
    .cu-btn {
      margin-left: 10px;
    }
  }
  
  .breadcrumb {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    padding: 8px 12px;
    margin-bottom: 10px;
    background-color: #f8f9fa;
    border-radius: 4px;
    
    .breadcrumb-item {
      display: flex;
      align-items: center;
      
      .breadcrumb-text {
        color: #1890ff;
        font-size: 14px;
      }
      
      .breadcrumb-separator {
        margin: 0 5px;
        color: #999;
      }
    }
  }
  
  .list-container {
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 6px rgba(0, 0, 0, 0.1);
    margin-bottom: 10px;
    overflow: hidden;
  }
  
  .dept-list {
    .dept-item {
      display: flex;
      align-items: center;
      padding: 15px;
      border-bottom: 1px solid #f0f0f0;
      
      &:last-child {
        border-bottom: none;
      }
      
      .dept-icon {
        margin-right: 10px;
        color: #1890ff;
      }
      
      .dept-content {
        flex: 1;
        
        .dept-title {
          font-size: 16px;
          color: #333;
          margin-bottom: 4px;
        }
        
        .dept-info {
          font-size: 12px;
          color: #999;
          
          .dept-leader {
            margin-right: 10px;
          }
        }
      }
      
      .dept-arrow {
        color: #999;
      }
    }
  }
  
  .user-list {
    .user-item {
      display: flex;
      align-items: center;
      padding: 12px 15px;
      border-bottom: 1px solid #f0f0f0;
      
      &:last-child {
        border-bottom: none;
      }
      
      .user-avatar {
        width: 40px;
        height: 40px;
        margin-right: 10px;
        
        .avatar-image {
          width: 100%;
          height: 100%;
          border-radius: 50%;
        }
      }
      
      .user-content {
        flex: 1;
        
        .user-name {
          font-size: 15px;
          color: #333;
          margin-bottom: 4px;
        }
        
        .user-info {
          display: flex;
          align-items: center;
          font-size: 12px;
          color: #999;
          
          .user-username {
            margin-right: 10px;
          }
        }
      }
      
      .user-actions {
        display: flex;
        align-items: center;
      }
    }
  }
  
  .empty-tip {
    padding: 30px 0;
    text-align: center;
    color: #999;
    font-size: 14px;
  }
  
  .pagination-box {
    display: flex;
    justify-content: center;
    margin-top: 10px;
  }
  
  .popup-dialog-box {
    background-color: #fff;
    border-radius: 8px;
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }
  
  .popup-title {
    font-size: 16px;
    font-weight: bold;
    padding: 15px;
    text-align: center;
    border-bottom: 1px solid #eee;
  }
  
  .form-content {
    padding: 15px;
    overflow-y: auto;
    max-height: calc(80vh - 120px);
  }
  
  .popup-footer {
    display: flex;
    justify-content: flex-end;
    padding: 10px 15px;
    border-top: 1px solid #eee;
  }
  
  .margin-left {
    margin-left: 10px;
  }
  
  .margin-left-sm {
    margin-left: 5px;
  }
  
  .text-green {
    color: #07c160;
  }
  
  .text-red {
    color: #e54d42;
  }

  .status-switch-container {
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .status-text {
    font-size: 14px;
    font-weight: bold;
  }

  .dept-picker {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: #fff;
    font-size: 14px;
    color: #333;
  }
</style>
