<template>
  <view class="container">
    <view class="test-section">
      <text class="title">编辑状态测试</text>
      
      <!-- 模拟编辑表单 -->
      <view class="form-section">
        <text class="form-title">模拟用户编辑</text>
        
        <view class="form-item">
          <text class="label">用户名称:</text>
          <input v-model="testForm.username" placeholder="请输入用户名称" />
        </view>
        
        <view class="form-item">
          <text class="label">用户昵称:</text>
          <input v-model="testForm.nickname" placeholder="请输入用户昵称" />
        </view>
        
        <view class="form-item">
          <text class="label">用户状态:</text>
          <view class="status-switch-container">
            <switch :checked="testForm.status === 0" @change="handleStatusChange" />
            <text class="status-text" :class="testForm.status === 0 ? 'text-green' : 'text-red'">
              {{ testForm.status === 0 ? '正常' : '停用' }}
            </text>
          </view>
        </view>
        
        <view class="form-item">
          <text class="label">当前状态值:</text>
          <text>{{ testForm.status }} ({{ typeof testForm.status }})</text>
        </view>
        
        <view class="button-group">
          <button @click="resetForm" class="cu-btn bg-grey">重置</button>
          <button @click="saveForm" class="cu-btn bg-blue">保存</button>
        </view>
      </view>
      
      <!-- 日志显示 -->
      <view class="log-section">
        <text class="log-title">操作日志:</text>
        <view class="log-content">
          <text v-for="(log, index) in logs" :key="index" class="log-item">
            {{ log }}
          </text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      testForm: {
        id: 1,
        username: 'testuser',
        nickname: '测试用户',
        status: 0
      },
      logs: []
    }
  },
  methods: {
    handleStatusChange(e) {
      const oldStatus = this.testForm.status
      this.testForm.status = e.detail.value ? 0 : 1
      this.addLog(`状态变化: ${oldStatus} -> ${this.testForm.status} (开关: ${e.detail.value})`)
    },
    
    resetForm() {
      this.testForm = {
        id: 1,
        username: 'testuser',
        nickname: '测试用户',
        status: 0
      }
      this.addLog('表单已重置')
    },
    
    saveForm() {
      this.addLog(`保存表单数据: ${JSON.stringify(this.testForm)}`)
      // 模拟保存成功
      setTimeout(() => {
        this.addLog('保存成功！')
      }, 500)
    },
    
    addLog(message) {
      const timestamp = new Date().toLocaleTimeString()
      this.logs.unshift(`[${timestamp}] ${message}`)
      if (this.logs.length > 10) {
        this.logs.pop()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  padding: 20px;
}

.test-section {
  .title {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 20px;
    display: block;
  }
}

.form-section {
  margin-bottom: 20px;
  padding: 15px;
  border: 1px solid #eee;
  border-radius: 8px;
  background-color: #fafafa;
  
  .form-title {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 15px;
    display: block;
  }
  
  .form-item {
    margin-bottom: 15px;
    
    .label {
      display: block;
      margin-bottom: 5px;
      font-weight: bold;
    }
    
    input {
      width: 100%;
      padding: 8px;
      border: 1px solid #ddd;
      border-radius: 4px;
    }
  }
  
  .status-switch-container {
    display: flex;
    align-items: center;
    gap: 10px;
  }
  
  .status-text {
    font-size: 14px;
    font-weight: bold;
    
    &.text-green {
      color: #07c160;
    }
    
    &.text-red {
      color: #e54d42;
    }
  }
  
  .button-group {
    display: flex;
    gap: 10px;
    margin-top: 20px;
  }
}

.log-section {
  padding: 15px;
  border: 1px solid #eee;
  border-radius: 8px;
  background-color: #f9f9f9;
  
  .log-title {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 10px;
    display: block;
  }
  
  .log-content {
    max-height: 200px;
    overflow-y: auto;
  }
  
  .log-item {
    display: block;
    padding: 5px 0;
    border-bottom: 1px solid #eee;
    font-size: 12px;
    color: #666;
  }
}
</style>
