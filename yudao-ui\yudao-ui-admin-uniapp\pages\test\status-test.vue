<template>
  <view class="container">
    <view class="test-section">
      <text class="title">状态显示测试</text>
      
      <view class="test-item">
        <text>状态值 0 (启用):</text>
        <text class="status" :class="0 === 0 ? 'text-green' : 'text-red'">
          {{ 0 === 0 ? '正常' : '停用' }}
        </text>
      </view>
      
      <view class="test-item">
        <text>状态值 1 (禁用):</text>
        <text class="status" :class="1 === 0 ? 'text-green' : 'text-red'">
          {{ 1 === 0 ? '正常' : '停用' }}
        </text>
      </view>
      
      <view class="test-item">
        <text>测试用户列表:</text>
        <view v-for="(user, index) in testUsers" :key="index" class="user-item">
          <text>{{ user.username }} - </text>
          <text class="status" :class="user.status === 0 ? 'text-green' : 'text-red'">
            {{ user.status === 0 ? '正常' : '停用' }}
          </text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      testUsers: [
        { username: 'admin', status: 0 },
        { username: 'test1', status: 1 },
        { username: 'test2', status: 0 }
      ]
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  padding: 20px;
}

.test-section {
  .title {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 20px;
  }
  
  .test-item {
    margin-bottom: 15px;
    padding: 10px;
    border: 1px solid #eee;
    border-radius: 4px;
  }
  
  .user-item {
    margin: 5px 0;
    padding: 5px;
    background-color: #f9f9f9;
    border-radius: 4px;
  }
  
  .status {
    font-weight: bold;
    
    &.text-green {
      color: #67C23A;
    }
    
    &.text-red {
      color: #F56C6C;
    }
  }
}
</style>
