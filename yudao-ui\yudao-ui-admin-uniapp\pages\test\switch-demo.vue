<template>
  <view class="container">
    <view class="demo-section">
      <text class="title">状态开关演示</text>
      
      <!-- 原始开关 -->
      <view class="demo-item">
        <text class="demo-title">原始开关（不够明显）:</text>
        <view class="status-switch-container">
          <switch :checked="status1 === 0" @change="handleStatus1Change" />
          <text class="status-text" :class="status1 === 0 ? 'text-green' : 'text-red'">
            {{ status1 === 0 ? '正常' : '停用' }}
          </text>
        </view>
      </view>
      
      <!-- 改进的开关 -->
      <view class="demo-item">
        <text class="demo-title">改进的开关（更明显）:</text>
        <status-switch :value="status2" @change="handleStatus2Change" />
      </view>
      
      <!-- 测试数据 -->
      <view class="demo-item">
        <text class="demo-title">当前状态值:</text>
        <text>状态1: {{ status1 }} ({{ status1 === 0 ? '正常' : '停用' }})</text>
        <text>状态2: {{ status2 }} ({{ status2 === 0 ? '正常' : '停用' }})</text>
      </view>
    </view>
  </view>
</template>

<script>
import StatusSwitch from '@/components/status-switch/status-switch.vue'

export default {
  components: {
    StatusSwitch
  },
  data() {
    return {
      status1: 0,
      status2: 0
    }
  },
  methods: {
    handleStatus1Change(e) {
      this.status1 = e.detail.value ? 0 : 1
      console.log('状态1变化:', this.status1)
    },
    handleStatus2Change(newStatus) {
      this.status2 = newStatus
      console.log('状态2变化:', this.status2)
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  padding: 20px;
}

.demo-section {
  .title {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 20px;
    display: block;
  }
  
  .demo-item {
    margin-bottom: 20px;
    padding: 15px;
    border: 1px solid #eee;
    border-radius: 8px;
    background-color: #fafafa;
  }
  
  .demo-title {
    font-size: 14px;
    font-weight: bold;
    margin-bottom: 10px;
    display: block;
  }
  
  .status-switch-container {
    display: flex;
    align-items: center;
    gap: 10px;
  }
  
  .status-text {
    font-size: 14px;
    font-weight: bold;
    
    &.text-green {
      color: #07c160;
    }
    
    &.text-red {
      color: #e54d42;
    }
  }
}
</style>
