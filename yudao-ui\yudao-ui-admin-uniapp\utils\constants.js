/**
 * 系统常量定义
 * 
 * <AUTHOR>
 */

// ========== 通用状态枚举 ==========
export const CommonStatusEnum = {
  ENABLE: 0,    // 启用/正常
  DISABLE: 1    // 禁用/停用
}

// 状态显示文本映射
export const StatusTextMap = {
  [CommonStatusEnum.ENABLE]: '正常',
  [CommonStatusEnum.DISABLE]: '停用'
}

// 状态样式类映射
export const StatusClassMap = {
  [CommonStatusEnum.ENABLE]: 'text-green',
  [CommonStatusEnum.DISABLE]: 'text-red'
}

// ========== 用户相关常量 ==========
export const UserConstants = {
  // 性别枚举
  SEX: {
    MALE: 1,      // 男
    FEMALE: 2,    // 女
    UNKNOWN: 0    // 未知
  }
}

// ========== 菜单相关常量 ==========
export const MenuConstants = {
  TYPE: {
    DIR: 1,       // 目录
    MENU: 2,      // 菜单
    BUTTON: 3     // 按钮
  }
}

// ========== 角色相关常量 ==========
export const RoleConstants = {
  TYPE: {
    SYSTEM: 1,    // 内置角色
    CUSTOM: 2     // 自定义角色
  }
}

// ========== 数据权限范围枚举 ==========
export const DataScopeEnum = {
  ALL: 1,                 // 全部数据权限
  DEPT_CUSTOM: 2,         // 指定部门数据权限
  DEPT_ONLY: 3,           // 部门数据权限
  DEPT_AND_CHILD: 4,      // 部门及以下数据权限
  DEPT_SELF: 5            // 仅本人数据权限
}

// ========== 工具函数 ==========

/**
 * 获取状态显示文本
 * @param {number} status 状态值
 * @returns {string} 状态文本
 */
export function getStatusText(status) {
  return StatusTextMap[status] || '未知'
}

/**
 * 获取状态样式类
 * @param {number} status 状态值
 * @returns {string} 样式类名
 */
export function getStatusClass(status) {
  return StatusClassMap[status] || ''
}

/**
 * 判断状态是否为启用
 * @param {number} status 状态值
 * @returns {boolean} 是否启用
 */
export function isStatusEnable(status) {
  return status === CommonStatusEnum.ENABLE
}

/**
 * 判断状态是否为禁用
 * @param {number} status 状态值
 * @returns {boolean} 是否禁用
 */
export function isStatusDisable(status) {
  return status === CommonStatusEnum.DISABLE
}
